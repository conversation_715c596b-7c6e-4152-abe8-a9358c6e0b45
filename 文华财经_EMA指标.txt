// 文华财经EMA指标
// 转换自通达信EMA指标
// 版本：1.0
// 作者：AI助手

// 参数设置
INPUT:MA_TYPE(1,1,5,1);
INPUT:MA_LEN(14,5,100,1);
INPUT:SHOW_EMA9(1,0,1,1);
INPUT:SHOW_EMA12(1,0,1,1);
INPUT:SHOW_EMA26(1,0,1,1);
INPUT:SHOW_EMA50(1,0,1,1);

// EMA计算函数
EMA9:=EMA(CLOSE,9);
EMA12:=EMA(CLOSE,12);
EMA26:=EMA(CLOSE,26);
EMA50:=EMA(CLOSE,50);

// 平滑移动平均线计算（保持与原通达信指标一致）
SMOOTH_MA:IF(MA_TYPE==1,MA(EMA9,MA_LEN),
IF(MA_TYPE==2,EMA(EMA9,MA_LEN),
IF(MA_TYPE==3,DMA(EMA9,1/MA_LEN),
IF(MA_TYPE==4,WMA(EMA9,MA_LEN),
IF(MA_TYPE==5,SUM(EMA9*VOL,MA_LEN)/SUM(VOL,MA_LEN),
EMA9)))));

// 绘制主平滑移动平均线
SMOOTH_MA COLORRED LINETHICK2;

// 绘制其他EMA线（根据开关设置）
IF SHOW_EMA9 THEN EMA9 COLORYELLOW LINETHICK1;
IF SHOW_EMA12 THEN EMA12 COLORBLUE LINETHICK1;
IF SHOW_EMA26 THEN EMA26 COLORGREEN LINETHICK1;
IF SHOW_EMA50 THEN EMA50 COLORMAGENTA LINETHICK1;

// 添加标题和参数显示
DRAWTEXT_FIX(1,0.01,0.01,0,'文华财经EMA指标') COLORWHITE;
DRAWTEXT_FIX(1,0.01,0.05,0,'MA类型: '+NUMTOSTR(MA_TYPE,0)) COLORGRAY;
DRAWTEXT_FIX(1,0.01,0.09,0,'周期: '+NUMTOSTR(MA_LEN,0)) COLORGRAY;

// 添加数值显示（在最后一条K线显示）
DRAWNUMBER(ISLASTBAR,C,SMOOTH_MA,2) COLORRED;
IF SHOW_EMA9 THEN DRAWNUMBER(ISLASTBAR,C,EMA9,2) COLORYELLOW;
IF SHOW_EMA12 THEN DRAWNUMBER(ISLASTBAR,C,EMA12,2) COLORBLUE;
IF SHOW_EMA26 THEN DRAWNUMBER(ISLASTBAR,C,EMA26,2) COLORGREEN;
IF SHOW_EMA50 THEN DRAWNUMBER(ISLASTBAR,C,EMA50,2) COLORMAGENTA;

// 添加交叉信号（可选功能）
CROSS_UP:=CROSS(CLOSE,SMOOTH_MA);
CROSS_DOWN:=CROSS(SMOOTH_MA,CLOSE);

DRAWICON(CROSS_UP,LOW*0.998,1);
DRAWICON(CROSS_DOWN,HIGH*1.002,2);

// 添加趋势判断
TREND_UP:=CLOSE>SMOOTH_MA;
TREND_DOWN:=CLOSE<SMOOTH_MA;

DRAWCOLORKLINE(TREND_UP,COLORRED,0);
DRAWCOLORKLINE(TREND_DOWN,COLORGREEN,0);

// 添加统计信息
TOTAL_BARS:=BARSCOUNT(CLOSE);
DRAWTEXT_FIX(1,0.8,0.01,0,'总K线数: '+NUMTOSTR(TOTAL_BARS,0)) COLORGRAY;
