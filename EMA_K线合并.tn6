N:9,1,100,1;
STYPE:0,0,6,1;
SLEN:14,1,50,1;
BMULT:2.0,0.1,5.0,0.1;
DINTVL:2,1,10,1;

EMA1:EMA(CLOSE,N);

SMAS:=IF(STYPE=1 OR STYPE=2,SMA(EMA1,SLEN,1),0);
EMAS:=IF(STYPE=3,EMA(EMA1,SLEN),0);
SMMAS:=IF(STYPE=4,SMA(EMA1,SLEN,1),0);

WMAS:=IF(STYPE=5,(EMA1*SLEN + REF(EMA1,1)*(SLEN-1) + REF(EMA1,2)*(SLEN-2) + REF(EMA1,3)*(SLEN-3) + REF(EMA1,4)*(SLEN-4) + REF(EMA1,5)*(SLEN-5) + REF(EMA1,6)*(SLEN-6) + REF(EMA1,7)*(SLEN-7) + REF(EMA1,8)*(SLEN-8) + REF(EMA1,9)*(SLEN-9) + REF(EMA1,10)*(SLEN-10) + REF(EMA1,11)*(SLEN-11) + REF(EMA1,12)*(SLEN-12) + REF(EMA1,13)*(SLEN-13) + REF(EMA1,14)*(SLEN-14)) / (SLEN + (SLEN-1) + (SLEN-2) + (SLEN-3) + (SLEN-4) + (SLEN-5) + (SLEN-6) + (SLEN-7) + (SLEN-8) + (SLEN-9) + (SLEN-10) + (SLEN-11) + (SLEN-12) + (SLEN-13) + (SLEN-14)),0);
VWMAS:=IF(STYPE=6,SUM(EMA1*VOL,SLEN)/SUM(VOL,SLEN),0);

SMA1:=IF(STYPE=1 OR STYPE=2,SMAS,IF(STYPE=3,EMAS,IF(STYPE=4,SMMAS,IF(STYPE=5,WMAS,IF(STYPE=6,VWMAS,0)))));
BBSTD:=IF(STYPE=2,STD(EMA1,SLEN)*BMULT,0);
BBU:=IF(STYPE=2,SMA1+BBSTD,0);
BBL:=IF(STYPE=2,SMA1-BBSTD,0);

NEWDAY:=DATE<>REF(DATE,1);
CNT:=BARSLAST(NEWDAY)+1;

EMA1,COLORBLUE,LINETHICK2;
IF(STYPE>0,SMA1,DRAWNULL),COLORYELLOW,LINETHICK1;
IF(STYPE=2,BBU,DRAWNULL),COLORGREEN,LINETHICK1;
IF(STYPE=2,BBL,DRAWNULL),COLORGREEN,LINETHICK1;
STICKLINE(STYPE=2,BBU,BBL,0,0),COLORGREEN;
DRAWTEXT(MOD(CNT,DINTVL)=0,LOW*0.998,CNT),COLORYELLOW;


