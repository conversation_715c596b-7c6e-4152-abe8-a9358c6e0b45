{==========EMA参数设置==========}
{EMA基础参数}
EMA_PERIOD:=9;                    {EMA周期，默认9}
SOURCE:=CLOSE;           {数据源，默认收盘价}
OFFSET:=0;               {偏移量，默认0}

{平滑处理参数}
SMOOTH_TYPE:=0;          {平滑类型：0=无，1=SMA，2=SMA+BB，3=EMA，4=SMMA，5=WMA，6=VWMA}
SMOOTH_LEN:=14;          {平滑周期，默认14}
BB_MULT:=2.0;            {布林带标准差倍数，默认2.0}

{==========K线计数参数设置==========}
{标签大小选项}
SIZE_OPTION:=4;          {标签大小：1=自动, 2=巨大, 3=大, 4=正常, 5=小, 6=微小}
L_COLOR:=16753920; {标签文字颜色，默认橙色，十进制颜色值}
DISP_INTVL:=2;     {显示间隔，每隔多少根K线显示一次标签}

{==========EMA核心计算==========}
{主EMA计算}
EMA_LINE:EMA(SOURCE,EMA_PERIOD);

{平滑处理计算}
{SMA平滑}
SMA_SMOOTH:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,SMA(EMA_LINE,SMOOTH_LEN,1),0);

{EMA平滑}
EMA_SMOOTH:=IF(SMOOTH_TYPE=3,EMA(EMA_LINE,SMOOTH_LEN),0);

{SMMA平滑}
SMMA_SMOOTH:=IF(SMOOTH_TYPE=4,SMA(EMA_LINE,SMOOTH_LEN,1),0);

{WMA平滑}
WMA_SMOOTH:=IF(SMOOTH_TYPE=5,
    (EMA_LINE*SMOOTH_LEN + REF(EMA_LINE,1)*(SMOOTH_LEN-1) + REF(EMA_LINE,2)*(SMOOTH_LEN-2) + 
     REF(EMA_LINE,3)*(SMOOTH_LEN-3) + REF(EMA_LINE,4)*(SMOOTH_LEN-4) + REF(EMA_LINE,5)*(SMOOTH_LEN-5) + 
     REF(EMA_LINE,6)*(SMOOTH_LEN-6) + REF(EMA_LINE,7)*(SMOOTH_LEN-7) + REF(EMA_LINE,8)*(SMOOTH_LEN-8) + 
     REF(EMA_LINE,9)*(SMOOTH_LEN-9) + REF(EMA_LINE,10)*(SMOOTH_LEN-10) + REF(EMA_LINE,11)*(SMOOTH_LEN-11) + 
     REF(EMA_LINE,12)*(SMOOTH_LEN-12) + REF(EMA_LINE,13)*(SMOOTH_LEN-13) + REF(EMA_LINE,14)*(SMOOTH_LEN-14)) / 
    (SMOOTH_LEN + (SMOOTH_LEN-1) + (SMOOTH_LEN-2) + (SMOOTH_LEN-3) + (SMOOTH_LEN-4) + (SMOOTH_LEN-5) + 
     (SMOOTH_LEN-6) + (SMOOTH_LEN-7) + (SMOOTH_LEN-8) + (SMOOTH_LEN-9) + (SMOOTH_LEN-10) + (SMOOTH_LEN-11) + 
     (SMOOTH_LEN-12) + (SMOOTH_LEN-13) + (SMOOTH_LEN-14)),0);

{VWMA平滑}
VWMA_SMOOTH:=IF(SMOOTH_TYPE=6,
    SUM(EMA_LINE*VOL,SMOOTH_LEN)/SUM(VOL,SMOOTH_LEN),0);

{合并平滑结果}
SMOOTH_MA:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,SMA_SMOOTH,
           IF(SMOOTH_TYPE=3,EMA_SMOOTH,
           IF(SMOOTH_TYPE=4,SMMA_SMOOTH,
           IF(SMOOTH_TYPE=5,WMA_SMOOTH,
           IF(SMOOTH_TYPE=6,VWMA_SMOOTH,0)))));

{布林带计算}
BB_STD:=IF(SMOOTH_TYPE=2,STD(EMA_LINE,SMOOTH_LEN)*BB_MULT,0);
BB_UPPER:=IF(SMOOTH_TYPE=2,SMOOTH_MA+BB_STD,0);
BB_LOWER:=IF(SMOOTH_TYPE=2,SMOOTH_MA-BB_STD,0);

{==========K线计数核心计算==========}
{检测是否为新的一天}
IS_NEW_DAY:=WEEKDAY<>REF(WEEKDAY,1);

{当日K线计数}
BAR_COUNT:=SUM(IF(IS_NEW_DAY,0,1),0)+1;

{根据选择的大小选项设置标签大小}
LABEL_SIZE:=IF(SIZE_OPTION=2,3,
           IF(SIZE_OPTION=3,2,
           IF(SIZE_OPTION=5,0,
           IF(SIZE_OPTION=6,-1,1)))); {1=正常, 2=大, 3=巨大, 0=小, -1=微小}

{==========输出显示==========}
{主EMA线}
EMA_LINE,COLORBLUE,LINETHICK2;

{平滑MA线}
IF(SMOOTH_TYPE>0,SMOOTH_MA,DRAWNULL),COLORYELLOW,LINETHICK1;

{布林带上轨}
IF(SMOOTH_TYPE=2,BB_UPPER,DRAWNULL),COLORGREEN,LINETHICK1;

{布林带下轨}
IF(SMOOTH_TYPE=2,BB_LOWER,DRAWNULL),COLORGREEN,LINETHICK1;

{布林带填充区域}
STICKLINE(SMOOTH_TYPE=2,BB_UPPER,BB_LOWER,0,0),COLORGREEN;

{只在设定的间隔显示K线计数标签}
IF BAR_COUNT%DISP_INTVL=0 THEN BEGIN
    DRAWTEXT(ISLASTBAR=0, LOW*0.998, NUMTOSTR(BAR_COUNT,0)),COLORL_COLOR;
END;

{==========使用说明==========}
{EMA部分：}
{1. N：EMA周期参数，建议值5-50}
{2. SOURCE：数据源，可选OPEN、HIGH、LOW、CLOSE等}
{3. OFFSET：偏移量，正数向右偏移，负数向左偏移}
{4. SMOOTH_TYPE：平滑类型选择}
{   0=无平滑，只显示基础EMA}
{   1=SMA平滑}
{   2=SMA+BB}
{   3=EMA平滑}
{   4=SMMA平滑}
{   5=WMA平滑}
{   6=VWMA平滑}
{5. SMOOTH_LEN：平滑处理的周期}
{6. BB_MULT：布林带标准差倍数}

{K线计数部分：}
{7. SIZE_OPTION：标签大小选择}
{   1=自动, 2=巨大, 3=大, 4=正常, 5=小, 6=微小}
{8. LABEL_COLOR：标签文字颜色}
{9. DISPLAY_INTERVAL：显示间隔，建议值1-10}

{==========综合功能==========}
{该指标同时提供：}
{- 多种EMA平滑处理选项}
{- 布林带显示功能}
{- K线计数显示（在K线下方）}
{- 每天自动重置计数器}
{- 可调节的显示间隔}
