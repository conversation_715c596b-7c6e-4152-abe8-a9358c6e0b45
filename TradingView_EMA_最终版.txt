//文华财经WH8指标公式
//TradingView EMA指标最终转换版
//编写示范，依此入市，风险自负！

//基本EMA参数
INPUT:LEN(9,1,100,1,"EMA长度");
INPUT:SRC(0,0,2,1,"数据源",["收盘价","开盘价","最高价","最低价"]);
INPUT:OFFSET(0,-500,500,1,"偏移量");

//选择数据源
DATA_SRC:=IF(SRC==0,CLOSE,IF(SRC==1,OPEN,IF(SRC==2,HIGH,LOW)));

//EMA计算和绘制
EMA_LINE:EMA(DATA_SRC,LEN) COLORBLUE LINETHICK2;

//平滑MA参数设置
INPUT:MA_TYPE(0,0,6,1,"平滑类型",["无","SMA","SMA+布林带","EMA","SMMA(RMA)","WMA","VWMA"]);
INPUT:MA_LEN(14,1,100,1,"平滑长度");
INPUT:BB_MULT(2.0,0.001,50,0.5,"布林带倍数");

//判断是否启用布林带
IS_BB:=MA_TYPE==2;
ENABLE_MA:=MA_TYPE!=0;

//平滑MA计算
SMOOTHING_MA:IF(ENABLE_MA,
    IF(MA_TYPE==1,MA(EMA_LINE,MA_LEN),
    IF(MA_TYPE==2,MA(EMA_LINE,MA_LEN),
    IF(MA_TYPE==3,EMA(EMA_LINE,MA_LEN),
    IF(MA_TYPE==4,DMA(EMA_LINE,1/MA_LEN),
    IF(MA_TYPE==5,WMA(EMA_LINE,MA_LEN),
    IF(MA_TYPE==6,SUM(EMA_LINE*VOL,MA_LEN)/SUM(VOL,MA_LEN),
    EMA_LINE)))))),
    DRAWNULL) COLORYELLOW LINETHICK1;

//布林带计算
SMOOTHING_STDEV:IF(IS_BB,STD(EMA_LINE,MA_LEN)*BB_MULT,DRAWNULL);
BB_UPPER:IF(IS_BB,SMOOTHING_MA+SMOOTHING_STDEV,DRAWNULL) COLORGREEN LINETHICK1;
BB_LOWER:IF(IS_BB,SMOOTHING_MA-SMOOTHING_STDEV,DRAWNULL) COLORGREEN LINETHICK1;

//价格突破信号
BREAKOUT_UP:=C>REF(C,1)&&H>REF(H,1);
BREAKOUT_DOWN:=C<REF(C,1)&&L<REF(L,1);
BREAKOUT_UP,ICON(0,'ICO8');
BREAKOUT_DOWN,ICON(1,'ICO9');

//参考高低点
H20:HHV(H,20),NODRAW;
L20:LLV(L,20),NODRAW;

//显示信息
DRAWTEXT_FIX(1,0.01,0.01,0,'TradingView EMA指标') COLORWHITE;
DRAWTEXT_FIX(1,0.01,0.05,0,'EMA:'+NUMTOSTR(LEN,0)) COLORBLUE;
DRAWTEXT_FIX(1,0.01,0.09,0,'长度:'+NUMTOSTR(MA_LEN,0)) COLORYELLOW;
IF IS_BB THEN DRAWTEXT_FIX(1,0.01,0.13,0,'倍数:'+NUMTOSTR(BB_MULT,1)) COLORGREEN;
DRAWTEXT_FIX(1,0.8,0.05,0,'20高:'+NUMTOSTR(H20,2)) COLORGRAY;
DRAWTEXT_FIX(1,0.8,0.09,0,'20低:'+NUMTOSTR(L20,2)) COLORGRAY;
