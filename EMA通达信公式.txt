{通达信EMA指标公式}
{功能：指数移动平均线及其平滑处理}
{转换自Pine Script v6版本}

{==========参数设置==========}
{EMA基础参数}
N:=9;                    {EMA周期，默认9}
SOURCE:=CLOSE;           {数据源，默认收盘价}
OFFSET:=0;               {偏移量，默认0}

{平滑处理参数}
SMOOTH_TYPE:=0;          {平滑类型：0=无，1=SMA，2=SMA+布林带，3=EMA，4=SMMA，5=WMA，6=VWMA}
SMOOTH_LEN:=14;          {平滑周期，默认14}
BB_MULT:=2.0;            {布林带标准差倍数，默认2.0}

{==========核心计算==========}
{主EMA计算}
EMA_LINE:EMA(SOURCE,N);

{平滑处理计算}
{SMA平滑}
SMA_SMOOTH:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,MA(EMA_LINE,SMOOTH_LEN),0);

{EMA平滑}
EMA_SMOOTH:=IF(SMOOTH_TYPE=3,EMA(EMA_LINE,SMOOTH_LEN),0);

{SMMA平滑（使用SMA近似）}
SMMA_SMOOTH:=IF(SMOOTH_TYPE=4,SMA(EMA_LINE,SMOOTH_LEN),0);

{WMA平滑}
WMA_SMOOTH:=IF(SMOOTH_TYPE=5,
    (EMA_LINE*SMOOTH_LEN + REF(EMA_LINE,1)*(SMOOTH_LEN-1) + REF(EMA_LINE,2)*(SMOOTH_LEN-2) + 
     REF(EMA_LINE,3)*(SMOOTH_LEN-3) + REF(EMA_LINE,4)*(SMOOTH_LEN-4) + REF(EMA_LINE,5)*(SMOOTH_LEN-5) + 
     REF(EMA_LINE,6)*(SMOOTH_LEN-6) + REF(EMA_LINE,7)*(SMOOTH_LEN-7) + REF(EMA_LINE,8)*(SMOOTH_LEN-8) + 
     REF(EMA_LINE,9)*(SMOOTH_LEN-9) + REF(EMA_LINE,10)*(SMOOTH_LEN-10) + REF(EMA_LINE,11)*(SMOOTH_LEN-11) + 
     REF(EMA_LINE,12)*(SMOOTH_LEN-12) + REF(EMA_LINE,13)*(SMOOTH_LEN-13) + REF(EMA_LINE,14)*(SMOOTH_LEN-14)) / 
    (SMOOTH_LEN + (SMOOTH_LEN-1) + (SMOOTH_LEN-2) + (SMOOTH_LEN-3) + (SMOOTH_LEN-4) + (SMOOTH_LEN-5) + 
     (SMOOTH_LEN-6) + (SMOOTH_LEN-7) + (SMOOTH_LEN-8) + (SMOOTH_LEN-9) + (SMOOTH_LEN-10) + (SMOOTH_LEN-11) + 
     (SMOOTH_LEN-12) + (SMOOTH_LEN-13) + (SMOOTH_LEN-14)),0);

{VWMA平滑（成交量加权移动平均）}
VWMA_SMOOTH:=IF(SMOOTH_TYPE=6,
    SUM(EMA_LINE*VOL,SMOOTH_LEN)/SUM(VOL,SMOOTH_LEN),0);

{合并平滑结果}
SMOOTH_MA:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,SMA_SMOOTH,
           IF(SMOOTH_TYPE=3,EMA_SMOOTH,
           IF(SMOOTH_TYPE=4,SMMA_SMOOTH,
           IF(SMOOTH_TYPE=5,WMA_SMOOTH,
           IF(SMOOTH_TYPE=6,VWMA_SMOOTH,0)))));

{布林带计算}
BB_STD:=IF(SMOOTH_TYPE=2,STD(EMA_LINE,SMOOTH_LEN)*BB_MULT,0);
BB_UPPER:=IF(SMOOTH_TYPE=2,SMOOTH_MA+BB_STD,0);
BB_LOWER:=IF(SMOOTH_TYPE=2,SMOOTH_MA-BB_STD,0);

{==========输出显示==========}
{主EMA线}
'EMA':EMA_LINE,COLORBLUE,LINETHICK2;

{平滑MA线}
'平滑MA':IF(SMOOTH_TYPE>0,SMOOTH_MA,DRAWNULL),COLORYELLOW,LINETHICK1;

{布林带上轨}
'布林上轨':IF(SMOOTH_TYPE=2,BB_UPPER,DRAWNULL),COLORGREEN,LINETHICK1;

{布林带下轨}
'布林下轨':IF(SMOOTH_TYPE=2,BB_LOWER,DRAWNULL),COLORGREEN,LINETHICK1;

{布林带填充区域}
STICKLINE(SMOOTH_TYPE=2,BB_UPPER,BB_LOWER,0,0),COLORGREEN;

{==========使用说明==========}
{1. N：EMA周期参数，建议值5-50}
{2. SOURCE：数据源，可选OPEN、HIGH、LOW、CLOSE等}
{3. OFFSET：偏移量，正数向右偏移，负数向左偏移}
{4. SMOOTH_TYPE：平滑类型选择}
{   0=无平滑，只显示基础EMA}
{   1=SMA平滑}
{   2=SMA平滑+布林带}
{   3=EMA平滑}
{   4=SMMA平滑}
{   5=WMA平滑}
{   6=VWMA平滑}
{5. SMOOTH_LEN：平滑处理的周期}
{6. BB_MULT：布林带标准差倍数}

{==========颜色说明==========}
{蓝色线：主EMA线}
{黄色线：平滑处理后的MA线}
{绿色线：布林带上下轨}
{绿色填充：布林带区域}

{==========输出线条名称==========}
{'EMA'：主指数移动平均线}
{'平滑MA'：平滑处理后的移动平均线}
{'布林上轨'：布林带上轨线}
{'布林下轨'：布林带下轨线}
