//文华财经K线计数指标
//功能：显示每日K线数量计数，只显示偶数数字
//如需显示更多数字，可添加相应的DRAWTEXT语句

今日期:DATE;
是新日:今日期<>REF(DATE,1);
计数器:IF(是新日,1,BARSLAST(是新日)+1);
//每天从第一根K线开始计数显示，只显示偶数数字
计数显示:IF(是新日,计数器,IF(MOD(计数器,2)=0,计数器,0));

DRAWTEXT(计数显示=2,LOW-0.5*(HIGH-LOW),'2'),RGB(217,108,0);
DRAWTEXT(计数显示=4,LOW-0.5*(HIGH-LOW),'4'),RGB(217,108,0);
DRAWTEXT(计数显示=6,LOW-0.5*(HIGH-LOW),'6'),RGB(217,108,0);
DRAWTEXT(计数显示=8,LOW-0.5*(HIGH-LOW),'8'),RGB(217,108,0);
DRAWTEXT(计数显示=10,LOW-0.5*(HIGH-LOW),'10'),RGB(217,108,0);
DRAWTEXT(计数显示=12,LOW-0.5*(HIGH-LOW),'12'),RGB(217,108,0);
DRAWTEXT(计数显示=14,LOW-0.5*(HIGH-LOW),'14'),RGB(217,108,0);
DRAWTEXT(计数显示=16,LOW-0.5*(HIGH-LOW),'16'),RGB(217,108,0);
DRAWTEXT(计数显示=18,LOW-0.5*(HIGH-LOW),'18'),RGB(217,108,0);
DRAWTEXT(计数显示=20,LOW-0.5*(HIGH-LOW),'20'),RGB(217,108,0);
DRAWTEXT(计数显示=22,LOW-0.5*(HIGH-LOW),'22'),RGB(217,108,0);
DRAWTEXT(计数显示=24,LOW-0.5*(HIGH-LOW),'24'),RGB(217,108,0);
DRAWTEXT(计数显示=26,LOW-0.5*(HIGH-LOW),'26'),RGB(217,108,0);
DRAWTEXT(计数显示=28,LOW-0.5*(HIGH-LOW),'28'),RGB(217,108,0);
DRAWTEXT(计数显示=30,LOW-0.5*(HIGH-LOW),'30'),RGB(217,108,0);
DRAWTEXT(计数显示=32,LOW-0.5*(HIGH-LOW),'32'),RGB(217,108,0);
DRAWTEXT(计数显示=34,LOW-0.5*(HIGH-LOW),'34'),RGB(217,108,0);
DRAWTEXT(计数显示=36,LOW-0.5*(HIGH-LOW),'36'),RGB(217,108,0);
DRAWTEXT(计数显示=38,LOW-0.5*(HIGH-LOW),'38'),RGB(217,108,0);
DRAWTEXT(计数显示=40,LOW-0.5*(HIGH-LOW),'40'),RGB(217,108,0);
DRAWTEXT(计数显示=42,LOW-0.5*(HIGH-LOW),'42'),RGB(217,108,0);
DRAWTEXT(计数显示=44,LOW-0.5*(HIGH-LOW),'44'),RGB(217,108,0);
DRAWTEXT(计数显示=46,LOW-0.5*(HIGH-LOW),'46'),RGB(217,108,0);
DRAWTEXT(计数显示=48,LOW-0.5*(HIGH-LOW),'48'),RGB(217,108,0);
DRAWTEXT(计数显示=50,LOW-0.5*(HIGH-LOW),'50'),RGB(217,108,0);
DRAWTEXT(计数显示=52,LOW-0.5*(HIGH-LOW),'52'),RGB(217,108,0);
DRAWTEXT(计数显示=54,LOW-0.5*(HIGH-LOW),'54'),RGB(217,108,0);
DRAWTEXT(计数显示=56,LOW-0.5*(HIGH-LOW),'56'),RGB(217,108,0);
DRAWTEXT(计数显示=58,LOW-0.5*(HIGH-LOW),'58'),RGB(217,108,0);
DRAWTEXT(计数显示=60,LOW-0.5*(HIGH-LOW),'60'),RGB(217,108,0);
DRAWTEXT(计数显示=62,LOW-0.5*(HIGH-LOW),'62'),RGB(217,108,0);
DRAWTEXT(计数显示=64,LOW-0.5*(HIGH-LOW),'64'),RGB(217,108,0);
DRAWTEXT(计数显示=66,LOW-0.5*(HIGH-LOW),'66'),RGB(217,108,0);
DRAWTEXT(计数显示=68,LOW-0.5*(HIGH-LOW),'68'),RGB(217,108,0);
DRAWTEXT(计数显示=70,LOW-0.5*(HIGH-LOW),'70'),RGB(217,108,0);
DRAWTEXT(计数显示=72,LOW-0.5*(HIGH-LOW),'72'),RGB(217,108,0);
DRAWTEXT(计数显示=74,LOW-0.5*(HIGH-LOW),'74'),RGB(217,108,0);
DRAWTEXT(计数显示=76,LOW-0.5*(HIGH-LOW),'76'),RGB(217,108,0);
DRAWTEXT(计数显示=78,LOW-0.5*(HIGH-LOW),'78'),RGB(217,108,0);
DRAWTEXT(计数显示=80,LOW-0.5*(HIGH-LOW),'80'),RGB(217,108,0);
DRAWTEXT(计数显示=82,LOW-0.5*(HIGH-LOW),'82'),RGB(217,108,0);
DRAWTEXT(计数显示=84,LOW-0.5*(HIGH-LOW),'84'),RGB(217,108,0);
DRAWTEXT(计数显示=86,LOW-0.5*(HIGH-LOW),'86'),RGB(217,108,0);
DRAWTEXT(计数显示=88,LOW-0.5*(HIGH-LOW),'88'),RGB(217,108,0);
DRAWTEXT(计数显示=90,LOW-0.5*(HIGH-LOW),'90'),RGB(217,108,0);
DRAWTEXT(计数显示=92,LOW-0.5*(HIGH-LOW),'92'),RGB(217,108,0);
DRAWTEXT(计数显示=94,LOW-0.5*(HIGH-LOW),'94'),RGB(217,108,0);
DRAWTEXT(计数显示=96,LOW-0.5*(HIGH-LOW),'96'),RGB(217,108,0);
DRAWTEXT(计数显示=98,LOW-0.5*(HIGH-LOW),'98'),RGB(217,108,0);
DRAWTEXT(计数显示=100,LOW-0.5*(HIGH-LOW),'100'),RGB(217,108,0);
DRAWTEXT(计数显示=102,LOW-0.5*(HIGH-LOW),'102'),RGB(217,108,0);
DRAWTEXT(计数显示=104,LOW-0.5*(HIGH-LOW),'104'),RGB(217,108,0);
DRAWTEXT(计数显示=106,LOW-0.5*(HIGH-LOW),'106'),RGB(217,108,0);
DRAWTEXT(计数显示=108,LOW-0.5*(HIGH-LOW),'108'),RGB(217,108,0);
DRAWTEXT(计数显示=110,LOW-0.5*(HIGH-LOW),'110'),RGB(217,108,0);
DRAWTEXT(计数显示=112,LOW-0.5*(HIGH-LOW),'112'),RGB(217,108,0);
DRAWTEXT(计数显示=114,LOW-0.5*(HIGH-LOW),'114'),RGB(217,108,0);
DRAWTEXT(计数显示=116,LOW-0.5*(HIGH-LOW),'116'),RGB(217,108,0);
DRAWTEXT(计数显示=118,LOW-0.5*(HIGH-LOW),'118'),RGB(217,108,0);
DRAWTEXT(计数显示=120,LOW-0.5*(HIGH-LOW),'120'),RGB(217,108,0);
DRAWTEXT(计数显示=122,LOW-0.5*(HIGH-LOW),'122'),RGB(217,108,0);
DRAWTEXT(计数显示=124,LOW-0.5*(HIGH-LOW),'124'),RGB(217,108,0);
DRAWTEXT(计数显示=126,LOW-0.5*(HIGH-LOW),'126'),RGB(217,108,0);
DRAWTEXT(计数显示=128,LOW-0.5*(HIGH-LOW),'128'),RGB(217,108,0);
//如需显示更多数字，请按照相同格式继续添加DRAWTEXT语句
//例如：DRAWTEXT(计数显示=201,LOW-0.5*(HIGH-LOW),'201'),RGB(217,108,0);
计数显示;
