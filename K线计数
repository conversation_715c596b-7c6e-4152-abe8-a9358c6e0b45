//文华财经K线计数指标
//功能：显示每日K线数量计数，只显示偶数数字
//计数范围：2-200，覆盖全天交易时段

今日期:DATE;                                    // 获取当前日期
是新日:今日期<>REF(DATE,1);                      // 判断是否为新的交易日
计数器:IF(是新日,1,BARSLAST(是新日)+1);           // 计算当日K线序号
显示位置:LOW-1.2*(HIGH-LOW);                     // 设置文字显示位置（K线明显下方）

// 显示偶数K线的计数（扩展范围2-200）
DRAWTEXT(计数器=2,显示位置,'2'),RGB(217,108,0);
DRAWTEXT(计数器=4,显示位置,'4'),RGB(217,108,0);
DRAWTEXT(计数器=6,显示位置,'6'),RGB(217,108,0);
DRAWTEXT(计数器=8,显示位置,'8'),RGB(217,108,0);
DRAWTEXT(计数器=10,显示位置,'10'),RGB(217,108,0);
DRAWTEXT(计数器=12,显示位置,'12'),RGB(217,108,0);
DRAWTEXT(计数器=14,显示位置,'14'),RGB(217,108,0);
DRAWTEXT(计数器=16,显示位置,'16'),RGB(217,108,0);
DRAWTEXT(计数器=18,显示位置,'18'),RGB(217,108,0);
DRAWTEXT(计数器=20,显示位置,'20'),RGB(217,108,0);
DRAWTEXT(计数器=22,显示位置,'22'),RGB(217,108,0);
DRAWTEXT(计数器=24,显示位置,'24'),RGB(217,108,0);
DRAWTEXT(计数器=26,显示位置,'26'),RGB(217,108,0);
DRAWTEXT(计数器=28,显示位置,'28'),RGB(217,108,0);
DRAWTEXT(计数器=30,显示位置,'30'),RGB(217,108,0);
DRAWTEXT(计数器=32,显示位置,'32'),RGB(217,108,0);
DRAWTEXT(计数器=34,显示位置,'34'),RGB(217,108,0);
DRAWTEXT(计数器=36,显示位置,'36'),RGB(217,108,0);
DRAWTEXT(计数器=38,显示位置,'38'),RGB(217,108,0);
DRAWTEXT(计数器=40,显示位置,'40'),RGB(217,108,0);
DRAWTEXT(计数器=42,显示位置,'42'),RGB(217,108,0);
DRAWTEXT(计数器=44,显示位置,'44'),RGB(217,108,0);
DRAWTEXT(计数器=46,显示位置,'46'),RGB(217,108,0);
DRAWTEXT(计数器=48,显示位置,'48'),RGB(217,108,0);
DRAWTEXT(计数器=50,显示位置,'50'),RGB(217,108,0);
DRAWTEXT(计数器=52,显示位置,'52'),RGB(217,108,0);
DRAWTEXT(计数器=54,显示位置,'54'),RGB(217,108,0);
DRAWTEXT(计数器=56,显示位置,'56'),RGB(217,108,0);
DRAWTEXT(计数器=58,显示位置,'58'),RGB(217,108,0);
DRAWTEXT(计数器=60,显示位置,'60'),RGB(217,108,0);
DRAWTEXT(计数器=62,显示位置,'62'),RGB(217,108,0);
DRAWTEXT(计数器=64,显示位置,'64'),RGB(217,108,0);
DRAWTEXT(计数器=66,显示位置,'66'),RGB(217,108,0);
DRAWTEXT(计数器=68,显示位置,'68'),RGB(217,108,0);
DRAWTEXT(计数器=70,显示位置,'70'),RGB(217,108,0);
DRAWTEXT(计数器=72,显示位置,'72'),RGB(217,108,0);
DRAWTEXT(计数器=74,显示位置,'74'),RGB(217,108,0);
DRAWTEXT(计数器=76,显示位置,'76'),RGB(217,108,0);
DRAWTEXT(计数器=78,显示位置,'78'),RGB(217,108,0);
DRAWTEXT(计数器=80,显示位置,'80'),RGB(217,108,0);
DRAWTEXT(计数器=82,显示位置,'82'),RGB(217,108,0);
DRAWTEXT(计数器=84,显示位置,'84'),RGB(217,108,0);
DRAWTEXT(计数器=86,显示位置,'86'),RGB(217,108,0);
DRAWTEXT(计数器=88,显示位置,'88'),RGB(217,108,0);
DRAWTEXT(计数器=90,显示位置,'90'),RGB(217,108,0);
DRAWTEXT(计数器=92,显示位置,'92'),RGB(217,108,0);
DRAWTEXT(计数器=94,显示位置,'94'),RGB(217,108,0);
DRAWTEXT(计数器=96,显示位置,'96'),RGB(217,108,0);
DRAWTEXT(计数器=98,显示位置,'98'),RGB(217,108,0);
DRAWTEXT(计数器=100,显示位置,'100'),RGB(217,108,0);
DRAWTEXT(计数器=102,显示位置,'102'),RGB(217,108,0);
DRAWTEXT(计数器=104,显示位置,'104'),RGB(217,108,0);
DRAWTEXT(计数器=106,显示位置,'106'),RGB(217,108,0);
DRAWTEXT(计数器=108,显示位置,'108'),RGB(217,108,0);
DRAWTEXT(计数器=110,显示位置,'110'),RGB(217,108,0);
DRAWTEXT(计数器=112,显示位置,'112'),RGB(217,108,0);
DRAWTEXT(计数器=114,显示位置,'114'),RGB(217,108,0);
DRAWTEXT(计数器=116,显示位置,'116'),RGB(217,108,0);
DRAWTEXT(计数器=118,显示位置,'118'),RGB(217,108,0);
DRAWTEXT(计数器=120,显示位置,'120'),RGB(217,108,0);
DRAWTEXT(计数器=122,显示位置,'122'),RGB(217,108,0);
DRAWTEXT(计数器=124,显示位置,'124'),RGB(217,108,0);
DRAWTEXT(计数器=126,显示位置,'126'),RGB(217,108,0);
DRAWTEXT(计数器=128,显示位置,'128'),RGB(217,108,0);
DRAWTEXT(计数器=130,显示位置,'130'),RGB(217,108,0);
DRAWTEXT(计数器=132,显示位置,'132'),RGB(217,108,0);
DRAWTEXT(计数器=134,显示位置,'134'),RGB(217,108,0);
DRAWTEXT(计数器=136,显示位置,'136'),RGB(217,108,0);
DRAWTEXT(计数器=138,显示位置,'138'),RGB(217,108,0);
DRAWTEXT(计数器=140,显示位置,'140'),RGB(217,108,0);
DRAWTEXT(计数器=142,显示位置,'142'),RGB(217,108,0);
DRAWTEXT(计数器=144,显示位置,'144'),RGB(217,108,0);
DRAWTEXT(计数器=146,显示位置,'146'),RGB(217,108,0);
DRAWTEXT(计数器=148,显示位置,'148'),RGB(217,108,0);
DRAWTEXT(计数器=150,显示位置,'150'),RGB(217,108,0);
DRAWTEXT(计数器=152,显示位置,'152'),RGB(217,108,0);
DRAWTEXT(计数器=154,显示位置,'154'),RGB(217,108,0);
DRAWTEXT(计数器=156,显示位置,'156'),RGB(217,108,0);
DRAWTEXT(计数器=158,显示位置,'158'),RGB(217,108,0);
DRAWTEXT(计数器=160,显示位置,'160'),RGB(217,108,0);
DRAWTEXT(计数器=162,显示位置,'162'),RGB(217,108,0);
DRAWTEXT(计数器=164,显示位置,'164'),RGB(217,108,0);
DRAWTEXT(计数器=166,显示位置,'166'),RGB(217,108,0);
DRAWTEXT(计数器=168,显示位置,'168'),RGB(217,108,0);
DRAWTEXT(计数器=170,显示位置,'170'),RGB(217,108,0);
DRAWTEXT(计数器=172,显示位置,'172'),RGB(217,108,0);
DRAWTEXT(计数器=174,显示位置,'174'),RGB(217,108,0);
DRAWTEXT(计数器=176,显示位置,'176'),RGB(217,108,0);
DRAWTEXT(计数器=178,显示位置,'178'),RGB(217,108,0);
DRAWTEXT(计数器=180,显示位置,'180'),RGB(217,108,0);
DRAWTEXT(计数器=182,显示位置,'182'),RGB(217,108,0);
DRAWTEXT(计数器=184,显示位置,'184'),RGB(217,108,0);
DRAWTEXT(计数器=186,显示位置,'186'),RGB(217,108,0);
DRAWTEXT(计数器=188,显示位置,'188'),RGB(217,108,0);
DRAWTEXT(计数器=190,显示位置,'190'),RGB(217,108,0);
DRAWTEXT(计数器=192,显示位置,'192'),RGB(217,108,0);
DRAWTEXT(计数器=194,显示位置,'194'),RGB(217,108,0);
DRAWTEXT(计数器=196,显示位置,'196'),RGB(217,108,0);
DRAWTEXT(计数器=198,显示位置,'198'),RGB(217,108,0);
DRAWTEXT(计数器=200,显示位置,'200'),RGB(217,108,0);
