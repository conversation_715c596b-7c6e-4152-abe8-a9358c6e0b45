//文华财经K线计数指标
//功能：显示每日K线数量计数，只显示偶数数字
//计数范围：2-200，覆盖全天交易时段

今日期:DATE;                                    // 获取当前日期
是新日:今日期<>REF(DATE,1);                      // 判断是否为新的交易日
计数器:IF(是新日,1,BARSLAST(是新日)+1);           // 计算当日K线序号
波动距离:MA(HIGH-LOW,14);                       // 使用14周期振幅均值作为动态距离

// 显示偶数K线的计数（扩展范围2-200）
// 使用ATR动态距离，适应不同周期和波动
DRAWTEXT(计数器=2,LOW-0.3*波动距离,'2'),RGB(217,108,0);
DRAWTEXT(计数器=4,LOW-0.3*波动距离,'4'),RGB(217,108,0);
DRAWTEXT(计数器=6,LOW-0.3*波动距离,'6'),RGB(217,108,0);
DRAWTEXT(计数器=8,LOW-0.3*波动距离,'8'),RGB(217,108,0);
DRAWTEXT(计数器=10,LOW-0.3*波动距离,'10'),RGB(217,108,0);
DRAWTEXT(计数器=12,LOW-0.3*波动距离,'12'),RGB(217,108,0);
DRAWTEXT(计数器=14,LOW-0.3*波动距离,'14'),RGB(217,108,0);
DRAWTEXT(计数器=16,LOW-0.3*波动距离,'16'),RGB(217,108,0);
DRAWTEXT(计数器=18,LOW-0.3*波动距离,'18'),RGB(217,108,0);
DRAWTEXT(计数器=20,LOW-0.3*波动距离,'20'),RGB(217,108,0);
DRAWTEXT(计数器=22,LOW-0.3*波动距离,'22'),RGB(217,108,0);
DRAWTEXT(计数器=24,LOW-0.3*波动距离,'24'),RGB(217,108,0);
DRAWTEXT(计数器=26,LOW-0.3*波动距离,'26'),RGB(217,108,0);
DRAWTEXT(计数器=28,LOW-0.3*波动距离,'28'),RGB(217,108,0);
DRAWTEXT(计数器=30,LOW-0.3*波动距离,'30'),RGB(217,108,0);
DRAWTEXT(计数器=32,LOW-0.3*波动距离,'32'),RGB(217,108,0);
DRAWTEXT(计数器=34,LOW-0.3*波动距离,'34'),RGB(217,108,0);
DRAWTEXT(计数器=36,LOW-0.3*波动距离,'36'),RGB(217,108,0);
DRAWTEXT(计数器=38,LOW-0.3*波动距离,'38'),RGB(217,108,0);
DRAWTEXT(计数器=40,LOW-0.3*波动距离,'40'),RGB(217,108,0);
DRAWTEXT(计数器=42,LOW-0.3*波动距离,'42'),RGB(217,108,0);
DRAWTEXT(计数器=44,LOW-0.3*波动距离,'44'),RGB(217,108,0);
DRAWTEXT(计数器=46,LOW-0.3*波动距离,'46'),RGB(217,108,0);
DRAWTEXT(计数器=48,LOW-0.3*波动距离,'48'),RGB(217,108,0);
DRAWTEXT(计数器=50,LOW-0.3*波动距离,'50'),RGB(217,108,0);
DRAWTEXT(计数器=52,LOW-0.3*波动距离,'52'),RGB(217,108,0);
DRAWTEXT(计数器=54,LOW-0.3*波动距离,'54'),RGB(217,108,0);
DRAWTEXT(计数器=56,LOW-0.3*波动距离,'56'),RGB(217,108,0);
DRAWTEXT(计数器=58,LOW-0.3*波动距离,'58'),RGB(217,108,0);
DRAWTEXT(计数器=60,LOW-0.3*波动距离,'60'),RGB(217,108,0);
DRAWTEXT(计数器=62,LOW-0.3*波动距离,'62'),RGB(217,108,0);
DRAWTEXT(计数器=64,LOW-0.3*波动距离,'64'),RGB(217,108,0);
DRAWTEXT(计数器=66,LOW-0.3*波动距离,'66'),RGB(217,108,0);
DRAWTEXT(计数器=68,LOW-0.3*波动距离,'68'),RGB(217,108,0);
DRAWTEXT(计数器=70,LOW-0.3*波动距离,'70'),RGB(217,108,0);
DRAWTEXT(计数器=72,LOW-0.3*波动距离,'72'),RGB(217,108,0);
DRAWTEXT(计数器=74,LOW-0.3*波动距离,'74'),RGB(217,108,0);
DRAWTEXT(计数器=76,LOW-0.3*波动距离,'76'),RGB(217,108,0);
DRAWTEXT(计数器=78,LOW-0.3*波动距离,'78'),RGB(217,108,0);
DRAWTEXT(计数器=80,LOW-0.3*波动距离,'80'),RGB(217,108,0);
DRAWTEXT(计数器=82,LOW-0.3*波动距离,'82'),RGB(217,108,0);
DRAWTEXT(计数器=84,LOW-0.3*波动距离,'84'),RGB(217,108,0);
DRAWTEXT(计数器=86,LOW-0.3*波动距离,'86'),RGB(217,108,0);
DRAWTEXT(计数器=88,LOW-0.3*波动距离,'88'),RGB(217,108,0);
DRAWTEXT(计数器=90,LOW-0.3*波动距离,'90'),RGB(217,108,0);
DRAWTEXT(计数器=92,LOW-0.3*波动距离,'92'),RGB(217,108,0);
DRAWTEXT(计数器=94,LOW-0.3*波动距离,'94'),RGB(217,108,0);
DRAWTEXT(计数器=96,LOW-0.3*波动距离,'96'),RGB(217,108,0);
DRAWTEXT(计数器=98,LOW-0.3*波动距离,'98'),RGB(217,108,0);
DRAWTEXT(计数器=100,LOW-0.3*波动距离,'100'),RGB(217,108,0);
DRAWTEXT(计数器=102,LOW-0.3*波动距离,'102'),RGB(217,108,0);
DRAWTEXT(计数器=104,LOW-0.3*波动距离,'104'),RGB(217,108,0);
DRAWTEXT(计数器=106,LOW-0.3*波动距离,'106'),RGB(217,108,0);
DRAWTEXT(计数器=108,LOW-0.3*波动距离,'108'),RGB(217,108,0);
DRAWTEXT(计数器=110,LOW-0.3*波动距离,'110'),RGB(217,108,0);
DRAWTEXT(计数器=112,LOW-0.3*波动距离,'112'),RGB(217,108,0);
DRAWTEXT(计数器=114,LOW-0.3*波动距离,'114'),RGB(217,108,0);
DRAWTEXT(计数器=116,LOW-0.3*波动距离,'116'),RGB(217,108,0);
DRAWTEXT(计数器=118,LOW-0.3*波动距离,'118'),RGB(217,108,0);
DRAWTEXT(计数器=120,LOW-0.3*波动距离,'120'),RGB(217,108,0);
DRAWTEXT(计数器=122,LOW-0.3*波动距离,'122'),RGB(217,108,0);
DRAWTEXT(计数器=124,LOW-0.3*波动距离,'124'),RGB(217,108,0);
DRAWTEXT(计数器=126,LOW-0.3*波动距离,'126'),RGB(217,108,0);
DRAWTEXT(计数器=128,LOW-0.3*波动距离,'128'),RGB(217,108,0);
DRAWTEXT(计数器=130,LOW-0.3*波动距离,'130'),RGB(217,108,0);
DRAWTEXT(计数器=132,LOW-0.3*波动距离,'132'),RGB(217,108,0);
DRAWTEXT(计数器=134,LOW-0.3*波动距离,'134'),RGB(217,108,0);
DRAWTEXT(计数器=136,LOW-0.3*波动距离,'136'),RGB(217,108,0);
DRAWTEXT(计数器=138,LOW-0.3*波动距离,'138'),RGB(217,108,0);
DRAWTEXT(计数器=140,LOW-0.3*波动距离,'140'),RGB(217,108,0);
DRAWTEXT(计数器=142,LOW-0.3*波动距离,'142'),RGB(217,108,0);
DRAWTEXT(计数器=144,LOW-0.3*波动距离,'144'),RGB(217,108,0);
DRAWTEXT(计数器=146,LOW-0.3*波动距离,'146'),RGB(217,108,0);
DRAWTEXT(计数器=148,LOW-0.3*波动距离,'148'),RGB(217,108,0);
DRAWTEXT(计数器=150,LOW-0.3*波动距离,'150'),RGB(217,108,0);
DRAWTEXT(计数器=152,LOW-0.3*波动距离,'152'),RGB(217,108,0);
DRAWTEXT(计数器=154,LOW-0.3*波动距离,'154'),RGB(217,108,0);
DRAWTEXT(计数器=156,LOW-0.3*波动距离,'156'),RGB(217,108,0);
DRAWTEXT(计数器=158,LOW-0.3*波动距离,'158'),RGB(217,108,0);
DRAWTEXT(计数器=160,LOW-0.3*波动距离,'160'),RGB(217,108,0);
DRAWTEXT(计数器=162,LOW-0.3*波动距离,'162'),RGB(217,108,0);
DRAWTEXT(计数器=164,LOW-0.3*波动距离,'164'),RGB(217,108,0);
DRAWTEXT(计数器=166,LOW-0.3*波动距离,'166'),RGB(217,108,0);
DRAWTEXT(计数器=168,LOW-0.3*波动距离,'168'),RGB(217,108,0);
DRAWTEXT(计数器=170,LOW-0.3*波动距离,'170'),RGB(217,108,0);
DRAWTEXT(计数器=172,LOW-0.3*波动距离,'172'),RGB(217,108,0);
DRAWTEXT(计数器=174,LOW-0.3*波动距离,'174'),RGB(217,108,0);
DRAWTEXT(计数器=176,LOW-0.3*波动距离,'176'),RGB(217,108,0);
DRAWTEXT(计数器=178,LOW-0.3*波动距离,'178'),RGB(217,108,0);
DRAWTEXT(计数器=180,LOW-0.3*波动距离,'180'),RGB(217,108,0);
DRAWTEXT(计数器=182,LOW-0.3*波动距离,'182'),RGB(217,108,0);
DRAWTEXT(计数器=184,LOW-0.3*波动距离,'184'),RGB(217,108,0);
DRAWTEXT(计数器=186,LOW-0.3*波动距离,'186'),RGB(217,108,0);
DRAWTEXT(计数器=188,LOW-0.3*波动距离,'188'),RGB(217,108,0);
DRAWTEXT(计数器=190,LOW-0.3*波动距离,'190'),RGB(217,108,0);
DRAWTEXT(计数器=192,LOW-0.3*波动距离,'192'),RGB(217,108,0);
DRAWTEXT(计数器=194,LOW-0.3*波动距离,'194'),RGB(217,108,0);
DRAWTEXT(计数器=196,LOW-0.3*波动距离,'196'),RGB(217,108,0);
DRAWTEXT(计数器=198,LOW-0.3*波动距离,'198'),RGB(217,108,0);
DRAWTEXT(计数器=200,LOW-0.3*波动距离,'200'),RGB(217,108,0);
