//@version=4
study("Bar Count", overlay=true, max_labels_count=500)

sizeOption = input(title="Label Size", type=input.string,
     options=["Auto", "Huge", "Large", "Normal", "Small", "Tiny"],
     defval="Normal")
     
labelSize = (sizeOption == "Huge") ? size.huge :
     (sizeOption == "Large") ? size.large :
     (sizeOption == "Small") ? size.small :
     (sizeOption == "Tiny") ? size.tiny :
     (sizeOption == "Auto") ? size.auto :
         size.normal     

color c_labelColor = input(color.orange, "Text Color", input.color)
c_contador = input(title="Display at every X bars", type=input.integer, defval=2)

is_new_day() => 
    d=dayofweek
    na(d[1]) or d != d[1]

var count = 1

if is_new_day()
    count := 1
else
    count := count + 1

if count % c_contador == 0
    label1 = label.new(bar_index, 0, style=label.style_none, text=tostring(count))
    label.set_textcolor(label1, c_labelColor)
    label.set_yloc(label1, yloc.belowbar)
    label.set_size(label1, labelSize)