//文华财经K线计数指标（简化版）
//功能：显示每日K线数量计数，只显示偶数数字
//由于文华财经DRAWTEXT限制，仍需要分别定义每个数字

今日期:DATE;                                    // 获取当前日期
是新日:今日期<>REF(DATE,1);                      // 判断是否为新的交易日
计数器:IF(是新日,1,BARSLAST(是新日)+1);           // 计算当日K线序号
显示位置:LOW-0.5*(HIGH-LOW);                     // 设置文字显示位置（K线下方）

// 只显示偶数K线的计数（常用范围2-50）
DRAWTEXT(计数器=2,显示位置,'2'),RGB(217,108,0);
DRAWTEXT(计数器=4,显示位置,'4'),RGB(217,108,0);
DRAWTEXT(计数器=6,显示位置,'6'),RGB(217,108,0);
DRAWTEXT(计数器=8,显示位置,'8'),RGB(217,108,0);
DRAWTEXT(计数器=10,显示位置,'10'),RGB(217,108,0);
DRAWTEXT(计数器=12,显示位置,'12'),RGB(217,108,0);
DRAWTEXT(计数器=14,显示位置,'14'),RGB(217,108,0);
DRAWTEXT(计数器=16,显示位置,'16'),RGB(217,108,0);
DRAWTEXT(计数器=18,显示位置,'18'),RGB(217,108,0);
DRAWTEXT(计数器=20,显示位置,'20'),RGB(217,108,0);
DRAWTEXT(计数器=22,显示位置,'22'),RGB(217,108,0);
DRAWTEXT(计数器=24,显示位置,'24'),RGB(217,108,0);
DRAWTEXT(计数器=26,显示位置,'26'),RGB(217,108,0);
DRAWTEXT(计数器=28,显示位置,'28'),RGB(217,108,0);
DRAWTEXT(计数器=30,显示位置,'30'),RGB(217,108,0);
DRAWTEXT(计数器=32,显示位置,'32'),RGB(217,108,0);
DRAWTEXT(计数器=34,显示位置,'34'),RGB(217,108,0);
DRAWTEXT(计数器=36,显示位置,'36'),RGB(217,108,0);
DRAWTEXT(计数器=38,显示位置,'38'),RGB(217,108,0);
DRAWTEXT(计数器=40,显示位置,'40'),RGB(217,108,0);
DRAWTEXT(计数器=42,显示位置,'42'),RGB(217,108,0);
DRAWTEXT(计数器=44,显示位置,'44'),RGB(217,108,0);
DRAWTEXT(计数器=46,显示位置,'46'),RGB(217,108,0);
DRAWTEXT(计数器=48,显示位置,'48'),RGB(217,108,0);
DRAWTEXT(计数器=50,显示位置,'50'),RGB(217,108,0);

// 显示当前计数值作为指标线
计数器;
