//文华财经K线计数指标
//功能：显示每日K线数量计数，只显示偶数数字
//计数范围：2-200，覆盖全天交易时段

今日期:DATE;                                    // 获取当前日期
是新日:今日期<>REF(DATE,1);                      // 判断是否为新的交易日
计数器:IF(是新日,1,BARSLAST(是新日)+1);           // 计算当日K线序号

// 显示偶数K线的计数（扩展范围2-200）
// 使用固定点数距离，让所有文字距离K线保持一致
DRAWTEXT(计数器=2,LOW-5,'2'),RGB(217,108,0);
DRAWTEXT(计数器=4,LOW-5,'4'),RGB(217,108,0);
DRAWTEXT(计数器=6,LOW-5,'6'),RGB(217,108,0);
DRAWTEXT(计数器=8,LOW-5,'8'),RGB(217,108,0);
DRAWTEXT(计数器=10,LOW-5,'10'),RGB(217,108,0);
DRAWTEXT(计数器=12,LOW-5,'12'),RGB(217,108,0);
DRAWTEXT(计数器=14,LOW-5,'14'),RGB(217,108,0);
DRAWTEXT(计数器=16,LOW-5,'16'),RGB(217,108,0);
DRAWTEXT(计数器=18,LOW-5,'18'),RGB(217,108,0);
DRAWTEXT(计数器=20,LOW-5,'20'),RGB(217,108,0);
DRAWTEXT(计数器=22,LOW-5,'22'),RGB(217,108,0);
DRAWTEXT(计数器=24,LOW-5,'24'),RGB(217,108,0);
DRAWTEXT(计数器=26,LOW-5,'26'),RGB(217,108,0);
DRAWTEXT(计数器=28,LOW-5,'28'),RGB(217,108,0);
DRAWTEXT(计数器=30,LOW-5,'30'),RGB(217,108,0);
DRAWTEXT(计数器=32,LOW-5,'32'),RGB(217,108,0);
DRAWTEXT(计数器=34,LOW-5,'34'),RGB(217,108,0);
DRAWTEXT(计数器=36,LOW-5,'36'),RGB(217,108,0);
DRAWTEXT(计数器=38,LOW-5,'38'),RGB(217,108,0);
DRAWTEXT(计数器=40,LOW-5,'40'),RGB(217,108,0);
DRAWTEXT(计数器=42,LOW-5,'42'),RGB(217,108,0);
DRAWTEXT(计数器=44,LOW-5,'44'),RGB(217,108,0);
DRAWTEXT(计数器=46,LOW-5,'46'),RGB(217,108,0);
DRAWTEXT(计数器=48,LOW-5,'48'),RGB(217,108,0);
DRAWTEXT(计数器=50,LOW-5,'50'),RGB(217,108,0);
DRAWTEXT(计数器=52,LOW-5,'52'),RGB(217,108,0);
DRAWTEXT(计数器=54,LOW-5,'54'),RGB(217,108,0);
DRAWTEXT(计数器=56,LOW-5,'56'),RGB(217,108,0);
DRAWTEXT(计数器=58,LOW-5,'58'),RGB(217,108,0);
DRAWTEXT(计数器=60,LOW-5,'60'),RGB(217,108,0);
DRAWTEXT(计数器=62,LOW-5,'62'),RGB(217,108,0);
DRAWTEXT(计数器=64,LOW-5,'64'),RGB(217,108,0);
DRAWTEXT(计数器=66,LOW-5,'66'),RGB(217,108,0);
DRAWTEXT(计数器=68,LOW-5,'68'),RGB(217,108,0);
DRAWTEXT(计数器=70,LOW-5,'70'),RGB(217,108,0);
DRAWTEXT(计数器=72,LOW-5,'72'),RGB(217,108,0);
DRAWTEXT(计数器=74,LOW-5,'74'),RGB(217,108,0);
DRAWTEXT(计数器=76,LOW-5,'76'),RGB(217,108,0);
DRAWTEXT(计数器=78,LOW-5,'78'),RGB(217,108,0);
DRAWTEXT(计数器=80,LOW-5,'80'),RGB(217,108,0);
DRAWTEXT(计数器=82,LOW-5,'82'),RGB(217,108,0);
DRAWTEXT(计数器=84,LOW-5,'84'),RGB(217,108,0);
DRAWTEXT(计数器=86,LOW-5,'86'),RGB(217,108,0);
DRAWTEXT(计数器=88,LOW-5,'88'),RGB(217,108,0);
DRAWTEXT(计数器=90,LOW-5,'90'),RGB(217,108,0);
DRAWTEXT(计数器=92,LOW-5,'92'),RGB(217,108,0);
DRAWTEXT(计数器=94,LOW-5,'94'),RGB(217,108,0);
DRAWTEXT(计数器=96,LOW-5,'96'),RGB(217,108,0);
DRAWTEXT(计数器=98,LOW-5,'98'),RGB(217,108,0);
DRAWTEXT(计数器=100,LOW-5,'100'),RGB(217,108,0);
DRAWTEXT(计数器=102,LOW-5,'102'),RGB(217,108,0);
DRAWTEXT(计数器=104,LOW-5,'104'),RGB(217,108,0);
DRAWTEXT(计数器=106,LOW-5,'106'),RGB(217,108,0);
DRAWTEXT(计数器=108,LOW-5,'108'),RGB(217,108,0);
DRAWTEXT(计数器=110,LOW-5,'110'),RGB(217,108,0);
DRAWTEXT(计数器=112,LOW-5,'112'),RGB(217,108,0);
DRAWTEXT(计数器=114,LOW-5,'114'),RGB(217,108,0);
DRAWTEXT(计数器=116,LOW-5,'116'),RGB(217,108,0);
DRAWTEXT(计数器=118,LOW-5,'118'),RGB(217,108,0);
DRAWTEXT(计数器=120,LOW-5,'120'),RGB(217,108,0);
DRAWTEXT(计数器=122,LOW-5,'122'),RGB(217,108,0);
DRAWTEXT(计数器=124,LOW-5,'124'),RGB(217,108,0);
DRAWTEXT(计数器=126,LOW-5,'126'),RGB(217,108,0);
DRAWTEXT(计数器=128,LOW-5,'128'),RGB(217,108,0);
DRAWTEXT(计数器=130,LOW-5,'130'),RGB(217,108,0);
DRAWTEXT(计数器=132,LOW-5,'132'),RGB(217,108,0);
DRAWTEXT(计数器=134,LOW-5,'134'),RGB(217,108,0);
DRAWTEXT(计数器=136,LOW-5,'136'),RGB(217,108,0);
DRAWTEXT(计数器=138,LOW-5,'138'),RGB(217,108,0);
DRAWTEXT(计数器=140,LOW-5,'140'),RGB(217,108,0);
DRAWTEXT(计数器=142,LOW-5,'142'),RGB(217,108,0);
DRAWTEXT(计数器=144,LOW-5,'144'),RGB(217,108,0);
DRAWTEXT(计数器=146,LOW-5,'146'),RGB(217,108,0);
DRAWTEXT(计数器=148,LOW-5,'148'),RGB(217,108,0);
DRAWTEXT(计数器=150,LOW-5,'150'),RGB(217,108,0);
DRAWTEXT(计数器=152,LOW-5,'152'),RGB(217,108,0);
DRAWTEXT(计数器=154,LOW-5,'154'),RGB(217,108,0);
DRAWTEXT(计数器=156,LOW-5,'156'),RGB(217,108,0);
DRAWTEXT(计数器=158,LOW-5,'158'),RGB(217,108,0);
DRAWTEXT(计数器=160,LOW-5,'160'),RGB(217,108,0);
DRAWTEXT(计数器=162,LOW-5,'162'),RGB(217,108,0);
DRAWTEXT(计数器=164,LOW-5,'164'),RGB(217,108,0);
DRAWTEXT(计数器=166,LOW-5,'166'),RGB(217,108,0);
DRAWTEXT(计数器=168,LOW-5,'168'),RGB(217,108,0);
DRAWTEXT(计数器=170,LOW-5,'170'),RGB(217,108,0);
DRAWTEXT(计数器=172,LOW-5,'172'),RGB(217,108,0);
DRAWTEXT(计数器=174,LOW-5,'174'),RGB(217,108,0);
DRAWTEXT(计数器=176,LOW-5,'176'),RGB(217,108,0);
DRAWTEXT(计数器=178,LOW-5,'178'),RGB(217,108,0);
DRAWTEXT(计数器=180,LOW-5,'180'),RGB(217,108,0);
DRAWTEXT(计数器=182,LOW-5,'182'),RGB(217,108,0);
DRAWTEXT(计数器=184,LOW-5,'184'),RGB(217,108,0);
DRAWTEXT(计数器=186,LOW-5,'186'),RGB(217,108,0);
DRAWTEXT(计数器=188,LOW-5,'188'),RGB(217,108,0);
DRAWTEXT(计数器=190,LOW-5,'190'),RGB(217,108,0);
DRAWTEXT(计数器=192,LOW-5,'192'),RGB(217,108,0);
DRAWTEXT(计数器=194,LOW-5,'194'),RGB(217,108,0);
DRAWTEXT(计数器=196,LOW-5,'196'),RGB(217,108,0);
DRAWTEXT(计数器=198,LOW-5,'198'),RGB(217,108,0);
DRAWTEXT(计数器=200,LOW-5,'200'),RGB(217,108,0);
