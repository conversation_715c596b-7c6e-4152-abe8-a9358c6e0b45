//文华财经K线计数指标
//功能：显示每日K线数量计数，只显示偶数数字
//计数范围：2-200，覆盖全天交易时段

今日期:DATE;                                    // 获取当前日期
是新日:今日期<>REF(DATE,1);                      // 判断是否为新的交易日
计数器:IF(是新日,1,BARSLAST(是新日)+1);           // 计算当日K线序号

// 显示偶数K线的计数（扩展范围2-200）
// 直接在DRAWTEXT中计算位置，避免产生均线
DRAWTEXT(计数器=2,LOW-1.2*(HIGH-LOW),'2'),RGB(217,108,0);
DRAWTEXT(计数器=4,LOW-1.2*(HIGH-LOW),'4'),RGB(217,108,0);
DRAWTEXT(计数器=6,LOW-1.2*(HIGH-LOW),'6'),RGB(217,108,0);
DRAWTEXT(计数器=8,LOW-1.2*(HIGH-LOW),'8'),RGB(217,108,0);
DRAWTEXT(计数器=10,LOW-1.2*(HIGH-LOW),'10'),RGB(217,108,0);
DRAWTEXT(计数器=12,LOW-1.2*(HIGH-LOW),'12'),RGB(217,108,0);
DRAWTEXT(计数器=14,LOW-1.2*(HIGH-LOW),'14'),RGB(217,108,0);
DRAWTEXT(计数器=16,LOW-1.2*(HIGH-LOW),'16'),RGB(217,108,0);
DRAWTEXT(计数器=18,LOW-1.2*(HIGH-LOW),'18'),RGB(217,108,0);
DRAWTEXT(计数器=20,LOW-1.2*(HIGH-LOW),'20'),RGB(217,108,0);
DRAWTEXT(计数器=22,LOW-1.2*(HIGH-LOW),'22'),RGB(217,108,0);
DRAWTEXT(计数器=24,LOW-1.2*(HIGH-LOW),'24'),RGB(217,108,0);
DRAWTEXT(计数器=26,LOW-1.2*(HIGH-LOW),'26'),RGB(217,108,0);
DRAWTEXT(计数器=28,LOW-1.2*(HIGH-LOW),'28'),RGB(217,108,0);
DRAWTEXT(计数器=30,LOW-1.2*(HIGH-LOW),'30'),RGB(217,108,0);
DRAWTEXT(计数器=32,LOW-1.2*(HIGH-LOW),'32'),RGB(217,108,0);
DRAWTEXT(计数器=34,LOW-1.2*(HIGH-LOW),'34'),RGB(217,108,0);
DRAWTEXT(计数器=36,LOW-1.2*(HIGH-LOW),'36'),RGB(217,108,0);
DRAWTEXT(计数器=38,LOW-1.2*(HIGH-LOW),'38'),RGB(217,108,0);
DRAWTEXT(计数器=40,LOW-1.2*(HIGH-LOW),'40'),RGB(217,108,0);
DRAWTEXT(计数器=42,LOW-1.2*(HIGH-LOW),'42'),RGB(217,108,0);
DRAWTEXT(计数器=44,LOW-1.2*(HIGH-LOW),'44'),RGB(217,108,0);
DRAWTEXT(计数器=46,LOW-1.2*(HIGH-LOW),'46'),RGB(217,108,0);
DRAWTEXT(计数器=48,LOW-1.2*(HIGH-LOW),'48'),RGB(217,108,0);
DRAWTEXT(计数器=50,LOW-1.2*(HIGH-LOW),'50'),RGB(217,108,0);
DRAWTEXT(计数器=52,LOW-1.2*(HIGH-LOW),'52'),RGB(217,108,0);
DRAWTEXT(计数器=54,LOW-1.2*(HIGH-LOW),'54'),RGB(217,108,0);
DRAWTEXT(计数器=56,LOW-1.2*(HIGH-LOW),'56'),RGB(217,108,0);
DRAWTEXT(计数器=58,LOW-1.2*(HIGH-LOW),'58'),RGB(217,108,0);
DRAWTEXT(计数器=60,LOW-1.2*(HIGH-LOW),'60'),RGB(217,108,0);
DRAWTEXT(计数器=62,LOW-1.2*(HIGH-LOW),'62'),RGB(217,108,0);
DRAWTEXT(计数器=64,LOW-1.2*(HIGH-LOW),'64'),RGB(217,108,0);
DRAWTEXT(计数器=66,LOW-1.2*(HIGH-LOW),'66'),RGB(217,108,0);
DRAWTEXT(计数器=68,LOW-1.2*(HIGH-LOW),'68'),RGB(217,108,0);
DRAWTEXT(计数器=70,LOW-1.2*(HIGH-LOW),'70'),RGB(217,108,0);
DRAWTEXT(计数器=72,LOW-1.2*(HIGH-LOW),'72'),RGB(217,108,0);
DRAWTEXT(计数器=74,LOW-1.2*(HIGH-LOW),'74'),RGB(217,108,0);
DRAWTEXT(计数器=76,LOW-1.2*(HIGH-LOW),'76'),RGB(217,108,0);
DRAWTEXT(计数器=78,LOW-1.2*(HIGH-LOW),'78'),RGB(217,108,0);
DRAWTEXT(计数器=80,LOW-1.2*(HIGH-LOW),'80'),RGB(217,108,0);
DRAWTEXT(计数器=82,LOW-1.2*(HIGH-LOW),'82'),RGB(217,108,0);
DRAWTEXT(计数器=84,LOW-1.2*(HIGH-LOW),'84'),RGB(217,108,0);
DRAWTEXT(计数器=86,LOW-1.2*(HIGH-LOW),'86'),RGB(217,108,0);
DRAWTEXT(计数器=88,LOW-1.2*(HIGH-LOW),'88'),RGB(217,108,0);
DRAWTEXT(计数器=90,LOW-1.2*(HIGH-LOW),'90'),RGB(217,108,0);
DRAWTEXT(计数器=92,LOW-1.2*(HIGH-LOW),'92'),RGB(217,108,0);
DRAWTEXT(计数器=94,LOW-1.2*(HIGH-LOW),'94'),RGB(217,108,0);
DRAWTEXT(计数器=96,LOW-1.2*(HIGH-LOW),'96'),RGB(217,108,0);
DRAWTEXT(计数器=98,LOW-1.2*(HIGH-LOW),'98'),RGB(217,108,0);
DRAWTEXT(计数器=100,LOW-1.2*(HIGH-LOW),'100'),RGB(217,108,0);
DRAWTEXT(计数器=102,LOW-1.2*(HIGH-LOW),'102'),RGB(217,108,0);
DRAWTEXT(计数器=104,LOW-1.2*(HIGH-LOW),'104'),RGB(217,108,0);
DRAWTEXT(计数器=106,LOW-1.2*(HIGH-LOW),'106'),RGB(217,108,0);
DRAWTEXT(计数器=108,LOW-1.2*(HIGH-LOW),'108'),RGB(217,108,0);
DRAWTEXT(计数器=110,LOW-1.2*(HIGH-LOW),'110'),RGB(217,108,0);
DRAWTEXT(计数器=112,LOW-1.2*(HIGH-LOW),'112'),RGB(217,108,0);
DRAWTEXT(计数器=114,LOW-1.2*(HIGH-LOW),'114'),RGB(217,108,0);
DRAWTEXT(计数器=116,LOW-1.2*(HIGH-LOW),'116'),RGB(217,108,0);
DRAWTEXT(计数器=118,LOW-1.2*(HIGH-LOW),'118'),RGB(217,108,0);
DRAWTEXT(计数器=120,LOW-1.2*(HIGH-LOW),'120'),RGB(217,108,0);
DRAWTEXT(计数器=122,LOW-1.2*(HIGH-LOW),'122'),RGB(217,108,0);
DRAWTEXT(计数器=124,LOW-1.2*(HIGH-LOW),'124'),RGB(217,108,0);
DRAWTEXT(计数器=126,LOW-1.2*(HIGH-LOW),'126'),RGB(217,108,0);
DRAWTEXT(计数器=128,LOW-1.2*(HIGH-LOW),'128'),RGB(217,108,0);
DRAWTEXT(计数器=130,LOW-1.2*(HIGH-LOW),'130'),RGB(217,108,0);
DRAWTEXT(计数器=132,LOW-1.2*(HIGH-LOW),'132'),RGB(217,108,0);
DRAWTEXT(计数器=134,LOW-1.2*(HIGH-LOW),'134'),RGB(217,108,0);
DRAWTEXT(计数器=136,LOW-1.2*(HIGH-LOW),'136'),RGB(217,108,0);
DRAWTEXT(计数器=138,LOW-1.2*(HIGH-LOW),'138'),RGB(217,108,0);
DRAWTEXT(计数器=140,LOW-1.2*(HIGH-LOW),'140'),RGB(217,108,0);
DRAWTEXT(计数器=142,LOW-1.2*(HIGH-LOW),'142'),RGB(217,108,0);
DRAWTEXT(计数器=144,LOW-1.2*(HIGH-LOW),'144'),RGB(217,108,0);
DRAWTEXT(计数器=146,LOW-1.2*(HIGH-LOW),'146'),RGB(217,108,0);
DRAWTEXT(计数器=148,LOW-1.2*(HIGH-LOW),'148'),RGB(217,108,0);
DRAWTEXT(计数器=150,LOW-1.2*(HIGH-LOW),'150'),RGB(217,108,0);
DRAWTEXT(计数器=152,LOW-1.2*(HIGH-LOW),'152'),RGB(217,108,0);
DRAWTEXT(计数器=154,LOW-1.2*(HIGH-LOW),'154'),RGB(217,108,0);
DRAWTEXT(计数器=156,LOW-1.2*(HIGH-LOW),'156'),RGB(217,108,0);
DRAWTEXT(计数器=158,LOW-1.2*(HIGH-LOW),'158'),RGB(217,108,0);
DRAWTEXT(计数器=160,LOW-1.2*(HIGH-LOW),'160'),RGB(217,108,0);
DRAWTEXT(计数器=162,LOW-1.2*(HIGH-LOW),'162'),RGB(217,108,0);
DRAWTEXT(计数器=164,LOW-1.2*(HIGH-LOW),'164'),RGB(217,108,0);
DRAWTEXT(计数器=166,LOW-1.2*(HIGH-LOW),'166'),RGB(217,108,0);
DRAWTEXT(计数器=168,LOW-1.2*(HIGH-LOW),'168'),RGB(217,108,0);
DRAWTEXT(计数器=170,LOW-1.2*(HIGH-LOW),'170'),RGB(217,108,0);
DRAWTEXT(计数器=172,LOW-1.2*(HIGH-LOW),'172'),RGB(217,108,0);
DRAWTEXT(计数器=174,LOW-1.2*(HIGH-LOW),'174'),RGB(217,108,0);
DRAWTEXT(计数器=176,LOW-1.2*(HIGH-LOW),'176'),RGB(217,108,0);
DRAWTEXT(计数器=178,LOW-1.2*(HIGH-LOW),'178'),RGB(217,108,0);
DRAWTEXT(计数器=180,LOW-1.2*(HIGH-LOW),'180'),RGB(217,108,0);
DRAWTEXT(计数器=182,LOW-1.2*(HIGH-LOW),'182'),RGB(217,108,0);
DRAWTEXT(计数器=184,LOW-1.2*(HIGH-LOW),'184'),RGB(217,108,0);
DRAWTEXT(计数器=186,LOW-1.2*(HIGH-LOW),'186'),RGB(217,108,0);
DRAWTEXT(计数器=188,LOW-1.2*(HIGH-LOW),'188'),RGB(217,108,0);
DRAWTEXT(计数器=190,LOW-1.2*(HIGH-LOW),'190'),RGB(217,108,0);
DRAWTEXT(计数器=192,LOW-1.2*(HIGH-LOW),'192'),RGB(217,108,0);
DRAWTEXT(计数器=194,LOW-1.2*(HIGH-LOW),'194'),RGB(217,108,0);
DRAWTEXT(计数器=196,LOW-1.2*(HIGH-LOW),'196'),RGB(217,108,0);
DRAWTEXT(计数器=198,LOW-1.2*(HIGH-LOW),'198'),RGB(217,108,0);
DRAWTEXT(计数器=200,LOW-1.2*(HIGH-LOW),'200'),RGB(217,108,0);
