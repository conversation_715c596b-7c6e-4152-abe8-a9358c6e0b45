//文华财经WH8指标公式
//TradingView EMA指标完美转换版
//编写示范，依此入市，风险自负！

//基本EMA参数
INPUT:LEN(9,1,100,1,"EMA长度");

//EMA计算
EMA_VALUE:EMA(CLOSE,LEN);

//绘制EMA线
EMA_VALUE,SETSTYLECOLOR(LINETHICK2,COLORBLUE);

//平滑MA参数
INPUT:MA_TYPE(1,1,3,1,"平滑类型",["SMA","EMA","WMA"]);
INPUT:MA_LEN(14,1,100,1,"平滑长度");

//平滑MA计算
SMOOTH_MA:IF(MA_TYPE==1,MA(EMA_VALUE,MA_LEN),
            IF(MA_TYPE==2,EMA(EMA_VALUE,MA_LEN),
            WMA(EMA_VALUE,MA_LEN))),SETSTYLECOLOR(LINETHICK1,COLORYELLOW);

//布林带参数
INPUT:BB_ENABLE(0,0,1,1,"启用布林带",["关闭","开启"]);
INPUT:BB_MULT(2.0,0.001,50,0.5,"布林带倍数");

//布林带计算
BB_MID:IF(BB_ENABLE==1,SMOOTH_MA,DRAWNULL);
BB_STD:IF(BB_ENABLE==1,STD(EMA_VALUE,MA_LEN)*BB_MULT,DRAWNULL);
BB_UPPER:IF(BB_ENABLE==1,BB_MID+BB_STD,DRAWNULL),SETSTYLECOLOR(LINETHICK1,COLORGREEN);
BB_LOWER:IF(BB_ENABLE==1,BB_MID-BB_STD,DRAWNULL),SETSTYLECOLOR(LINETHICK1,COLORGREEN);

//显示信息
DRAWTEXT(ISLASTBAR,EMA_VALUE,'EMA:'+NUMTOSTR(LEN,0)),COLORBLUE;
DRAWTEXT(ISLASTBAR,SMOOTH_MA,'MA:'+NUMTOSTR(MA_LEN,0)),COLORYELLOW;
IF BB_ENABLE==1 THEN DRAWTEXT(ISLASTBAR,BB_UPPER,'BB×'+NUMTOSTR(BB_MULT,1)),COLORGREEN;
