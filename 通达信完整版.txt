N1:20,1,100,1;
STYPE:0,0,6,1;
SLEN:14,1,50,1;
BMULT:2.0,0.1,5.0,0.1;

EMA1:EMA(CLOSE,N1);

SMAS:=IF(STYPE=1 OR STYPE=2,SMA(EMA1,SL<PERSON>),0);
EMAS:=IF(STYPE=3,EMA(EMA1,SLEN),0);
SMMAS:=IF(STYPE=4,SMA(EMA1,SLEN),0);
VWMAS:=IF(STYPE=6,SUM(EMA1*VOL,SLEN)/SUM(VOL,SLEN),0);
SMA1:=IF(STYPE=1 OR STYPE=2,SMAS,IF(STYPE=3,EMAS,IF(STYPE=4,SMMAS,IF(STYPE=6,VWMAS,0))));

BBSTD:=IF(STYPE=2,STD(EMA1,SLEN)*BMULT,0);
BBU:=IF(STYPE=2,SMA1+BBSTD,0);
BBL:=IF(STYPE=2,SMA1-BBSTD,0);

NEWDAY:=DATE<>REF(DATE,1);
CNT:=BARSLAST(NEWDAY)+1;

EMA1,COLORBLUE,LINETHICK2;
IF(STYPE>0,SMA1,DRAWNULL),COLORYELLOW,LINETHICK1;
IF(STYPE=2,BBU,DRAWNULL),COLORGREEN,LINETHICK1;
IF(STYPE=2,BBL,DRAWNULL),COLORGREEN,LINETHICK1;
STICKLINE(STYPE=2,BBU,BBL,0,0),COLORGREEN;

DRAWTEXT(CNT=2,LOW*0.99,'2'),COLORYELLOW;
DRAWTEXT(CNT=4,LOW*0.99,'4'),COLORYELLOW;
DRAWTEXT(CNT=6,LOW*0.99,'6'),COLORYELLOW;
DRAWTEXT(CNT=8,LOW*0.99,'8'),COLORYELLOW;
DRAWTEXT(CNT=10,LOW*0.99,'10'),COLORYELLOW;
DRAWTEXT(CNT=12,LOW*0.99,'12'),COLORYELLOW;
DRAWTEXT(CNT=14,LOW*0.99,'14'),COLORYELLOW;
DRAWTEXT(CNT=16,LOW*0.99,'16'),COLORYELLOW;
DRAWTEXT(CNT=18,LOW*0.99,'18'),COLORYELLOW;
DRAWTEXT(CNT=20,LOW*0.99,'20'),COLORYELLOW;
DRAWTEXT(CNT=22,LOW*0.99,'22'),COLORYELLOW;
DRAWTEXT(CNT=24,LOW*0.99,'24'),COLORYELLOW;
DRAWTEXT(CNT=26,LOW*0.99,'26'),COLORYELLOW;
DRAWTEXT(CNT=28,LOW*0.99,'28'),COLORYELLOW;
DRAWTEXT(CNT=30,LOW*0.99,'30'),COLORYELLOW;
DRAWTEXT(CNT=32,LOW*0.99,'32'),COLORYELLOW;
DRAWTEXT(CNT=34,LOW*0.99,'34'),COLORYELLOW;
DRAWTEXT(CNT=36,LOW*0.99,'36'),COLORYELLOW;
DRAWTEXT(CNT=38,LOW*0.99,'38'),COLORYELLOW;
DRAWTEXT(CNT=40,LOW*0.99,'40'),COLORYELLOW;
DRAWTEXT(CNT=42,LOW*0.99,'42'),COLORYELLOW;
DRAWTEXT(CNT=44,LOW*0.99,'44'),COLORYELLOW;
DRAWTEXT(CNT=46,LOW*0.99,'46'),COLORYELLOW;
DRAWTEXT(CNT=48,LOW*0.99,'48'),COLORYELLOW;
DRAWTEXT(CNT=50,LOW*0.99,'50'),COLORYELLOW;
