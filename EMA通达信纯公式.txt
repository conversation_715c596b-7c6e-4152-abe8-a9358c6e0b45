N:=9;
SOURCE:=CLOSE;
OFFSET:=0;
SMOOTH_TYPE:=0;
SMOOTH_LEN:=14;
BB_MULT:=2.0;

EMA_LINE:EMA(SOURCE,N);

SMA_SMOOTH:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,SMA(EMA_LINE,SMOOTH_LEN,1),0);
EMA_SMOOTH:=IF(SMOOTH_TYPE=3,EMA(EMA_LINE,SMOOTH_LEN),0);
SMMA_SMOOTH:=IF(SMOOTH_TYPE=4,SMA(EMA_LINE,SMOOTH_LEN,1),0);
WMA_SMOOTH:=IF(SMOOTH_TYPE=5,(EMA_LINE*SMOOTH_LEN + REF(EMA_LINE,1)*(SMOOTH_LEN-1) + REF(EMA_LINE,2)*(SMOOTH_LEN-2) + REF(EMA_LINE,3)*(SMOOTH_LEN-3) + REF(EMA_LINE,4)*(SMOOTH_LEN-4) + REF(EMA_LINE,5)*(SMOOTH_LEN-5) + REF(EMA_LINE,6)*(SMOOTH_LEN-6) + REF(EMA_LINE,7)*(SMOOTH_LEN-7) + REF(EMA_LINE,8)*(SMOOTH_LEN-8) + REF(EMA_LINE,9)*(SMOOTH_LEN-9) + REF(EMA_LINE,10)*(SMOOTH_LEN-10) + REF(EMA_LINE,11)*(SMOOTH_LEN-11) + REF(EMA_LINE,12)*(SMOOTH_LEN-12) + REF(EMA_LINE,13)*(SMOOTH_LEN-13) + REF(EMA_LINE,14)*(SMOOTH_LEN-14)) / (SMOOTH_LEN + (SMOOTH_LEN-1) + (SMOOTH_LEN-2) + (SMOOTH_LEN-3) + (SMOOTH_LEN-4) + (SMOOTH_LEN-5) + (SMOOTH_LEN-6) + (SMOOTH_LEN-7) + (SMOOTH_LEN-8) + (SMOOTH_LEN-9) + (SMOOTH_LEN-10) + (SMOOTH_LEN-11) + (SMOOTH_LEN-12) + (SMOOTH_LEN-13) + (SMOOTH_LEN-14)),0);
VWMA_SMOOTH:=IF(SMOOTH_TYPE=6,SUM(EMA_LINE*VOL,SMOOTH_LEN)/SUM(VOL,SMOOTH_LEN),0);

SMOOTH_MA:=IF(SMOOTH_TYPE=1 OR SMOOTH_TYPE=2,SMA_SMOOTH,IF(SMOOTH_TYPE=3,EMA_SMOOTH,IF(SMOOTH_TYPE=4,SMMA_SMOOTH,IF(SMOOTH_TYPE=5,WMA_SMOOTH,IF(SMOOTH_TYPE=6,VWMA_SMOOTH,0)))));

BB_STD:=IF(SMOOTH_TYPE=2,STD(EMA_LINE,SMOOTH_LEN)*BB_MULT,0);
BB_UPPER:=IF(SMOOTH_TYPE=2,SMOOTH_MA+BB_STD,0);
BB_LOWER:=IF(SMOOTH_TYPE=2,SMOOTH_MA-BB_STD,0);

EMA_LINE,COLORBLUE,LINETHICK2;
IF(SMOOTH_TYPE>0,SMOOTH_MA,DRAWNULL),COLORYELLOW,LINETHICK1;
IF(SMOOTH_TYPE=2,BB_UPPER,DRAWNULL),COLORGREEN,LINETHICK1;
IF(SMOOTH_TYPE=2,BB_LOWER,DRAWNULL),COLORGREEN,LINETHICK1;
STICKLINE(SMOOTH_TYPE=2,BB_UPPER,BB_LOWER,0,0),COLORGREEN;
