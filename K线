//@version=4  // 指定Pine Script版本为第4版
study("Bar Count", overlay=true, max_labels_count=500)  // 创建一个名为"Bar Count"的研究指标，覆盖在主图上，最多显示500个标签

// 创建标签大小选择输入框，用户可以选择标签的显示大小
sizeOption = input(title="Label Size", type=input.string,
     options=["Auto", "Huge", "Large", "Normal", "Small", "Tiny"],  // 提供6种大小选项
     defval="Normal")  // 默认值为"Normal"

// 根据用户选择的大小选项，设置实际的标签大小
labelSize = (sizeOption == "Huge") ? size.huge :  // 如果选择"Huge"，使用巨大尺寸
     (sizeOption == "Large") ? size.large :  // 如果选择"Large"，使用大尺寸
     (sizeOption == "Small") ? size.small :  // 如果选择"Small"，使用小尺寸
     (sizeOption == "Tiny") ? size.tiny :  // 如果选择"Tiny"，使用微小尺寸
     (sizeOption == "Auto") ? size.auto :  // 如果选择"Auto"，使用自动尺寸
         size.normal  // 默认使用正常尺寸

color c_labelColor = input(color.orange, "Text Color", input.color)  // 创建颜色输入框，用户可以选择标签文字颜色，默认为橙色
c_contador = input(title="Display at every X bars", type=input.integer, defval=2)  // 创建整数输入框，设置每隔多少根K线显示一次标签，默认为2

// 定义函数：检测是否为新的一天
is_new_day() =>
    d=dayofweek  // 获取当前K线的星期几
    na(d[1]) or d != d[1]  // 如果前一根K线的星期几为空值，或者当前星期几与前一根不同，则返回true

var count = 1  // 声明变量count，用于计数当天的K线数量，初始值为1，使用var关键字保持变量在K线间持续存在

// 如果是新的一天，重置计数器
if is_new_day()
    count := 1  // 将计数器重置为1
else
    count := count + 1  // 否则计数器加1

// 如果当前计数能被设定的间隔整除，则显示标签
if count % c_contador == 0
    label1 = label.new(bar_index, 0, style=label.style_none, text=tostring(count))  // 在当前K线位置创建新标签，显示当前计数值
    label.set_textcolor(label1, c_labelColor)  // 设置标签文字颜色为用户选择的颜色
    label.set_yloc(label1, yloc.belowbar)  // 设置标签位置在K线下方
    label.set_size(label1, labelSize)  // 设置标签大小为用户选择的大小