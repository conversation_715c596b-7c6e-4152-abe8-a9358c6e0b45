{通达信EMA+K线计数指标}
{可设置参数版本}

{参数定义}
N:9,1,100,1;
STYPE:0,0,6,1;
SLEN:14,1,50,1;
BMULT:2.0,0.1,5.0,0.1;
DINTVL:2,1,10,1;

{EMA计算}
EMA1:EMA(CLOSE,N);

{平滑处理}
SMAS:=IF(STYPE=1 OR STYPE=2,SMA(EMA1,SLEN),0);
EMAS:=IF(STYPE=3,EMA(EMA1,SLEN),0);
SMMAS:=IF(STYPE=4,SMA(EMA1,SLEN),0);
VWMAS:=IF(STYPE=6,SUM(EMA1*VOL,SLEN)/SUM(VOL,SLEN),0);
SMA1:=IF(STYPE=1 OR STYPE=2,SMAS,IF(STYPE=3,EMAS,IF(STYPE=4,SMMAS,IF(STYPE=6,VWMAS,0))));

{布林带计算}
BBSTD:=IF(STYPE=2,STD(EMA1,SLEN)*BMULT,0);
BBU:=IF(STYPE=2,SMA1+BBSTD,0);
BBL:=IF(STYPE=2,SMA1-BBSTD,0);

{K线计数}
NEWDAY:=DATE<>REF(DATE,1);
CNT:=BARSLAST(NEWDAY)+1;

{输出显示}
EMA1,COLORBLUE,LINETHICK2;
IF(STYPE>0,SMA1,DRAWNULL),COLORYELLOW,LINETHICK1;
IF(STYPE=2,BBU,DRAWNULL),COLORGREEN,LINETHICK1;
IF(STYPE=2,BBL,DRAWNULL),COLORGREEN,LINETHICK1;
STICKLINE(STYPE=2,BBU,BBL,0,0),COLORGREEN;
DRAWTEXT(MOD(CNT,DINTVL)=0,LOW*0.995,CNT),COLORYELLOW;
