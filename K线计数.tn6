{K线计数指标}
{功能：在K线下方显示当前K线计数，支持按天重置}

{==========参数设置==========}
{标签大小选项}
SIZE_OPTION:=1;          {标签大小：1=自动, 2=巨大, 3=大, 4=正常, 5=小, 6=微小}
LABEL_COLOR:=COLORORANGE; {标签文字颜色，默认橙色}
DISPLAY_INTERVAL:=2;     {显示间隔，每隔多少根K线显示一次标签}

{==========核心计算==========}
{检测是否为新的一天}
IS_NEW_DAY:=DAYOFWEEK<>REF(DAYOFWEEK,1) OR BARSCOUNT=1;

{初始化计数器}
COUNT:=1;
IF IS_NEW_DAY THEN
    COUNT:=1
ELSE
    COUNT:=REF(COUNT,1)+1;

{根据选择的大小选项设置标签大小}
LABEL_SIZE:=IF(SIZE_OPTION=2,3,
           IF(SIZE_OPTION=3,2,
           IF(SIZE_OPTION=5,0,
           IF(SIZE_OPTION=6,-1,1)))); {1=正常, 2=大, 3=巨大, 0=小, -1=微小}

{==========输出显示==========}
{只在设定的间隔显示标签}
IF COUNT%DISPLAY_INTERVAL=0 THEN BEGIN
    DRAWTEXT(ISLASTBAR=0, LOW*0.998, NUMTOSTR(COUNT,0)),COLORLABEL_COLOR;
END;

{==========使用说明==========}
{1. SIZE_OPTION：标签大小选择}
{   1=自动, 2=巨大, 3=大, 4=正常, 5=小, 6=微小}
{2. LABEL_COLOR：标签文字颜色}
{3. DISPLAY_INTERVAL：显示间隔，建议值1-10}

{==========颜色代码==========}
{COLORORANGE: 橙色}
{COLORRED: 红色}
{COLORGREEN: 绿色}
{COLORBLUE: 蓝色}
{COLORYELLOW: 黄色}
{COLORWHITE: 白色}
{COLORBLACK: 黑色}

{==========注意事项==========}
{1. 该指标在K线下方显示当前K线计数}
{2. 每天开始时计数器会自动重置为1}
{3. 可以设置显示间隔，避免过于密集的标签}
