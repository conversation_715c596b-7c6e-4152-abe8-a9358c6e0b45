//文华财经指标公式
//TradingView EMA指标转换版

//参数设置
LEN:=9;
SRC:=CLOSE;
OFFSET:=0;

//EMA计算
OUT:EMA(SRC,LEN);

//绘制EMA线
OUT COLORBLUE LINETHICK2;

//平滑MA参数设置
MA_TYPE:="None";  //可选: "None", "SMA", "SMA + Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"
MA_LEN:=14;
BB_MULT:=2.0;

//判断是否启用布林带
IS_BB:=MA_TYPE=="SMA + Bollinger Bands";
ENABLE_MA:=MA_TYPE!="None";

//平滑MA计算函数
MA_FUNC(SOURCE,LENGTH,MATYPE)=>
    IF MATYPE=="SMA" THEN MA(SOURCE,LENGTH)
    ELSE IF MATYPE=="SMA + Bollinger Bands" THEN MA(SOURCE,LENGTH)
    ELSE IF MATYPE=="EMA" THEN EMA(SOURCE,LENGTH)
    ELSE IF MATYPE=="SMMA (RMA)" THEN DMA(SOURCE,1/LENGTH)
    ELSE IF MATYPE=="WMA" THEN WMA(SOURCE,LENGTH)
    ELSE IF MATYPE=="VWMA" THEN SUM(SOURCE*VOL,LENGTH)/SUM(VOL,LENGTH)
    ELSE SOURCE;

//平滑MA计算
SMOOTHING_MA:IF(ENABLE_MA,MA_FUNC(OUT,MA_LEN,MA_TYPE),DRAWNULL);
SMOOTHING_STDEV:IF(IS_BB,STD(OUT,MA_LEN)*BB_MULT,DRAWNULL);

//绘制平滑MA线
SMOOTHING_MA COLORYELLOW LINETHICK1;

//绘制布林带
BB_UPPER:IF(IS_BB,SMOOTHING_MA+SMOOTHING_STDEV,DRAWNULL);
BB_LOWER:IF(IS_BB,SMOOTHING_MA-SMOOTHING_STDEV,DRAWNULL);

BB_UPPER COLORGREEN LINETHICK1;
BB_LOWER COLORGREEN LINETHICK1;

//填充布林带背景
//文华财经不支持直接填充，可以使用带状线替代
//DRAWLINE(BB_UPPER,BB_LOWER,COLORNEW(COLORGREEN,90));

//显示标题和参数
DRAWTEXT_FIX(1,0.01,0.01,0,'TradingView EMA指标') COLORWHITE;
DRAWTEXT_FIX(1,0.01,0.05,0,'EMA长度: '+NUMTOSTR(LEN,0)) COLORBLUE;
DRAWTEXT_FIX(1,0.01,0.09,0,'平滑类型: '+MA_TYPE) COLORYELLOW;
DRAWTEXT_FIX(1,0.01,0.13,0,'平滑长度: '+NUMTOSTR(MA_LEN,0)) COLORYELLOW;
IF IS_BB THEN DRAWTEXT_FIX(1,0.01,0.17,0,'布林带倍数: '+NUMTOSTR(BB_MULT,1)) COLORGREEN;
