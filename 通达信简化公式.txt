N:=9;
SRC:=CLOSE;
STYPE:=0;
SLEN:=14;
BMULT:=2.0;
DINTVL:=2;

EMA1:EMA(SRC,N);
SMAS:=IF(STYPE=1 OR STYPE=2,SMA(EMA1,SLEN,1),0);
EMAS:=IF(STYPE=3,EMA(EMA1,SLEN),0);
SMMAS:=IF(STYPE=4,SMA(EMA1,SLEN,1),0);
VWMAS:=IF(STYPE=6,SUM(EMA1*VOL,SLEN)/SUM(VOL,SLEN),0);
SMA1:=IF(STYPE=1 OR STYPE=2,SMAS,IF(STYPE=3,EMAS,IF(STYPE=4,SMMAS,IF(STYPE=6,VWMAS,0))));
BBSTD:=IF(STYPE=2,STD(EMA1,SLEN)*BMULT,0);
BBU:=IF(STYPE=2,SMA1+BBSTD,0);
BBL:=IF(STYPE=2,SMA1-BBSTD,0);

NEWDAY:=WEEKDAY<>REF(WEEKDAY,1);
CNT:=IF(NEWDAY,1,REF(CNT,1)+1);

EMA1,COLORBLUE,LINETHICK2;
IF(STYPE>0,SMA1,DRAWNULL),COLORYELLOW,LINETHICK1;
IF(STYPE=2,BBU,DRAWNULL),COLORGREEN,LINETHICK1;
IF(STYPE=2,BBL,DRAWNULL),COLORGREEN,LINETHICK1;
STICKLINE(STYPE=2,BBU,BBL,0,0),COLORGREEN;
DRAWTEXT(CNT%DINTVL=0,LOW*0.998,NUMTOSTR(CNT,0)),COLOR16753920;
