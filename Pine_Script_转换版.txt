//文华财经EMA指标（转换自Pine Script v6）
//功能：指数移动平均线及多种平滑处理

//==========参数设置==========
N:9;                        //EMA周期，默认9
数据源:CLOSE;               //数据源，默认收盘价
偏移量:0;                   //偏移量，默认0
平滑类型:0;                 //平滑类型：0=无，1=SMA，2=SMA+布林带，3=EMA，4=SMMA，5=WMA，6=VWMA
平滑周期:14;                //平滑周期，默认14
布林倍数:2.0;               //布林带标准差倍数，默认2.0

//==========核心计算==========
//主EMA计算
EMA主线:EMA(数据源,N);

//平滑处理计算
SMA平滑:IF(平滑类型=1 OR 平滑类型=2,MA(EMA主线,平滑周期),0);
EMA平滑:IF(平滑类型=3,EMA(EMA主线,平滑周期),0);
SMMA平滑:IF(平滑类型=4,SMA(EMA主线,平滑周期),0);
WMA平滑:IF(平滑类型=5,
    (EMA主线*平滑周期 + REF(EMA主线,1)*(平滑周期-1) + REF(EMA主线,2)*(平滑周期-2) + 
     REF(EMA主线,3)*(平滑周期-3) + REF(EMA主线,4)*(平滑周期-4) + REF(EMA主线,5)*(平滑周期-5) + 
     REF(EMA主线,6)*(平滑周期-6) + REF(EMA主线,7)*(平滑周期-7) + REF(EMA主线,8)*(平滑周期-8) + 
     REF(EMA主线,9)*(平滑周期-9) + REF(EMA主线,10)*(平滑周期-10) + REF(EMA主线,11)*(平滑周期-11) + 
     REF(EMA主线,12)*(平滑周期-12) + REF(EMA主线,13)*(平滑周期-13) + REF(EMA主线,14)*(平滑周期-14)) / 
    (平滑周期 + (平滑周期-1) + (平滑周期-2) + (平滑周期-3) + (平滑周期-4) + (平滑周期-5) + 
     (平滑周期-6) + (平滑周期-7) + (平滑周期-8) + (平滑周期-9) + (平滑周期-10) + (平滑周期-11) + 
     (平滑周期-12) + (平滑周期-13) + (平滑周期-14)),0);
VWMA平滑:IF(平滑类型=6,SUM(EMA主线*VOL,平滑周期)/SUM(VOL,平滑周期),0);

//合并平滑结果
平滑MA:IF(平滑类型=1 OR 平滑类型=2,SMA平滑,
       IF(平滑类型=3,EMA平滑,
       IF(平滑类型=4,SMMA平滑,
       IF(平滑类型=5,WMA平滑,
       IF(平滑类型=6,VWMA平滑,0)))));

//布林带计算
布林标准差:IF(平滑类型=2,STD(EMA主线,平滑周期)*布林倍数,0);
布林上轨:IF(平滑类型=2,平滑MA+布林标准差,0);
布林下轨:IF(平滑类型=2,平滑MA-布林标准差,0);

//==========输出显示==========
//主EMA线（蓝色）
EMA主线,RGB(0,0,255),LINETHICK2;

//平滑MA线（黄色，条件显示）
平滑MA线:IF(平滑类型>0,平滑MA,0);
平滑MA线,RGB(255,255,0),LINETHICK1;

//布林带上轨（绿色，条件显示）
布林上轨线:IF(平滑类型=2,布林上轨,0);
布林上轨线,RGB(0,255,0),LINETHICK1;

//布林带下轨（绿色，条件显示）
布林下轨线:IF(平滑类型=2,布林下轨,0);
布林下轨线,RGB(0,255,0),LINETHICK1;

//布林带填充区域（浅绿色）
STICKLINE(平滑类型=2,布林上轨,布林下轨,0,0),RGB(0,255,0);

//==========使用说明==========
//参数调整说明：
//1. N：EMA周期参数，建议值5-50
//2. 数据源：可选OPEN、HIGH、LOW、CLOSE等
//3. 偏移量：正数向右偏移，负数向左偏移
//4. 平滑类型：
//   0=无平滑，只显示基础EMA
//   1=SMA平滑
//   2=SMA平滑+布林带
//   3=EMA平滑
//   4=SMMA平滑
//   5=WMA平滑
//   6=VWMA平滑
//5. 平滑周期：平滑处理的周期
//6. 布林倍数：布林带标准差倍数

//==========转换对照==========
//Pine Script v6 → 文华财经麦语言
//ta.ema(src, len) → EMA(数据源,N)
//ta.sma(source, length) → MA(source,length)
//ta.rma() → SMA()近似
//ta.stdev() → STD()
//input.int() → 参数定义
//plot() → 输出线条
//color.blue → RGB(0,0,255)
//color.yellow → RGB(255,255,0)
//color.green → RGB(0,255,0)
