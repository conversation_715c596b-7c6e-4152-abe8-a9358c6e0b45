//文华财经WH8指标公式
//TradingView EMA指标优化转换版
//编写示范，依此入市，风险自负！

//基本EMA参数
INPUT:LEN(9,1,100,1,"EMA长度");
INPUT:SRC(0,0,2,1,"数据源",["收盘价","开盘价","最高价","最低价"]);
INPUT:OFFSET(0,-500,500,1,"偏移量");

//选择数据源
DATA_SRC:=IF(SRC==0,CLOSE,IF(SRC==1,OPEN,IF(SRC==2,HIGH,LOW)));

//EMA计算
OUT:EMA(DATA_SRC,LEN);

//绘制EMA线
OUT COLORBLUE LINETHICK2;

//平滑MA参数设置
INPUT:MA_TYPE(0,0,6,1,"平滑类型",["无","SMA","SMA+布林带","EMA","SMMA(RMA)","WMA","VWMA"]);
INPUT:MA_LEN(14,1,100,1,"平滑长度");
INPUT:BB_MULT(2.0,0.001,50,0.5,"布林带倍数");

//转换为字符串类型用于比较
MA_TYPE_STR:=IF(MA_TYPE==0,"None",
            IF(MA_TYPE==1,"SMA",
            IF(MA_TYPE==2,"SMA + Bollinger Bands",
            IF(MA_TYPE==3,"EMA",
            IF(MA_TYPE==4,"SMMA (RMA)",
            IF(MA_TYPE==5,"WMA","VWMA"))))));

//判断是否启用布林带
IS_BB:=MA_TYPE_STR=="SMA + Bollinger Bands";
ENABLE_MA:=MA_TYPE_STR!="None";

//平滑MA计算函数
MA_FUNC(SOURCE,LENGTH,MATYPE)=>
    IF MATYPE=="SMA" THEN MA(SOURCE,LENGTH)
    ELSE IF MATYPE=="SMA + Bollinger Bands" THEN MA(SOURCE,LENGTH)
    ELSE IF MATYPE=="EMA" THEN EMA(SOURCE,LENGTH)
    ELSE IF MATYPE=="SMMA (RMA)" THEN DMA(SOURCE,1/LENGTH)
    ELSE IF MATYPE=="WMA" THEN WMA(SOURCE,LENGTH)
    ELSE IF MATYPE=="VWMA" THEN SUM(SOURCE*VOL,LENGTH)/SUM(VOL,LENGTH)
    ELSE SOURCE;

//平滑MA计算
SMOOTHING_MA:IF(ENABLE_MA,MA_FUNC(OUT,MA_LEN,MA_TYPE_STR),DRAWNULL);
SMOOTHING_STDEV:IF(IS_BB,STD(OUT,MA_LEN)*BB_MULT,DRAWNULL);

//绘制平滑MA线 - 使用SETSTYLECOLOR
SMOOTHING_MA,SETSTYLECOLOR(LINETHICK1,COLORYELLOW);

//绘制布林带
BB_UPPER:IF(IS_BB,SMOOTHING_MA+SMOOTHING_STDEV,DRAWNULL);
BB_LOWER:IF(IS_BB,SMOOTHING_MA-SMOOTHING_STDEV,DRAWNULL);

BB_UPPER,SETSTYLECOLOR(LINETHICK1,COLORGREEN);
BB_LOWER,SETSTYLECOLOR(LINETHICK1,COLORGREEN);

//添加图标提示 - 价格突破信号
BREAKOUT_UP:=C>REF(C,1)&&H>REF(H,1);
BREAKOUT_DOWN:=C<REF(C,1)&&L<REF(L,1);

BREAKOUT_UP,ICON(0,'ICO8');  //上涨突破，红色三角图标
BREAKOUT_DOWN,ICON(1,'ICO9');  //下跌突破，绿色三角图标

//显示参考高低点（NODRAW只显示数值）
H20:HHV(H,20),NODRAW;  //20周期高点参考
L20:LLV(L,20),NODRAW;  //20周期低点参考

//显示标题和参数
DRAWTEXT_FIX(1,0.01,0.01,0,'TradingView EMA指标') COLORWHITE;
DRAWTEXT_FIX(1,0.01,0.05,0,'EMA长度: '+NUMTOSTR(LEN,0)) COLORBLUE;
DRAWTEXT_FIX(1,0.01,0.09,0,'平滑类型') COLORYELLOW;
DRAWTEXT_FIX(1,0.01,0.13,0,'平滑长度: '+NUMTOSTR(MA_LEN,0)) COLORYELLOW;
IF IS_BB THEN DRAWTEXT_FIX(1,0.01,0.17,0,'布林带倍数: '+NUMTOSTR(BB_MULT,1)) COLORGREEN;

//显示参考高低点数值
DRAWTEXT_FIX(1,0.8,0.05,0,'20周期高: '+NUMTOSTR(H20,2)) COLORGRAY;
DRAWTEXT_FIX(1,0.8,0.09,0,'20周期低: '+NUMTOSTR(L20,2)) COLORGRAY;
