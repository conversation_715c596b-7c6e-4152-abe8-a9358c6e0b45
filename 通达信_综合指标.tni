N:=2;

IS_MINUTE:=PERIOD<=4;
NEW_PERIOD:=IF(IS_MINUTE,DAY<>REF(DAY,1),YEAR<>REF(YEAR,1));

LAST_RESET:=BARSLAST(NEW_PERIOD);
BCOUNT:=LAST_RESET+1;

SHOWCOND:=MOD(BCOUNT,N)=0 AND BCOUNT>0;
DRAWNUMBER(SHOWCOND,LOW*0.995,BCOUNT),COLORRED;

MA_TYPE:=1;
MA_LEN:=14;
SMOOTH_MA:IF(MA_TYPE=1,MA(EMA(CLOSE,9),MA_LEN),
IF(MA_TYPE=2,EMA(EMA(CLOSE,9),MA_LEN),
IF(MA_TYPE=3,DMA(EMA(CLOSE,9),1/MA_LEN),
IF(MA_TYPE=4,WMA(EMA(CLOSE,9),MA_LEN),
IF(MA_TYPE=5,SUM(EMA(CLOS<PERSON>,9)*VOL,MA_LEN)/SUM(VOL,MA_LEN),
EMA(CLOSE,9))))));
SMOOTH_MA;
